const RaidMonitor = require('../src/services/raidMonitor');
const { config } = require('../src/config/config');

// Мокаем конфигурацию для тестов
jest.mock('../src/config/config', () => ({
  config: {
    ...jest.requireActual('../src/config/config').config,
    monitoring: {
      ...jest.requireActual('../src/config/config').config.monitoring,
      accountsReminderMinutes: 30
    }
  }
}));

// Мокаем cron
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  }))
}));

const cron = require('node-cron');

describe('Accounts Reminder Dynamic Cron Tests', () => {
  let raidMonitor;

  beforeEach(() => {
    jest.clearAllMocks();
    
    raidMonitor = new RaidMonitor();
    
    // Мокаем зависимости
    raidMonitor.sheetsService = {
      getClientsData: jest.fn()
    };
    
    raidMonitor.scheduleService = {
      getCombinedScheduleData: jest.fn()
    };
    
    raidMonitor.notificationService = {
      sendNotification: jest.fn()
    };
    
    raidMonitor.notifiedStore = {
      isNotified: jest.fn().mockReturnValue(false),
      markAsNotified: jest.fn()
    };
    
    raidMonitor.dynamicConfigService = {
      getAllowedRaidTypes: jest.fn().mockResolvedValue(config.allowedRaidTypes)
    };
    
    raidMonitor.internalScheduleService = {
      getInternalScheduleData: jest.fn().mockResolvedValue([])
    };
    
    raidMonitor.dynamicCronJobs = new Map();
    raidMonitor.initialized = true;
  });

  describe('setupDynamicAccountsChecks', () => {
    test('should create dynamic cron jobs for accounts checks', async () => {
      const futureRaids = [
        {
          date: '16.08.2025',
          time: '21:00',
          raidType: 'MO Heroic',
          status: 'открыт'
        },
        {
          date: '16.08.2025',
          time: '22:00',
          raidType: 'LoU Normal',
          status: 'открыт'
        }
      ];

      const now = new Date('2025-08-16T18:00:00Z'); // 20:00 CET

      await raidMonitor.setupDynamicAccountsChecks(futureRaids, now);

      // Проверяем, что cron.schedule был вызван для каждого уникального времени
      expect(cron.schedule).toHaveBeenCalledTimes(2);

      // Проверяем, что задачи добавлены в Map
      expect(raidMonitor.dynamicCronJobs.size).toBe(2);

      // Проверяем ключи задач (теперь группируются по времени)
      const jobKeys = Array.from(raidMonitor.dynamicCronJobs.keys());
      expect(jobKeys).toContain('accounts_16.08.2025|21:00');
      expect(jobKeys).toContain('accounts_16.08.2025|22:00');
    });

    test('should skip raids where accounts check time has passed', async () => {
      const futureRaids = [
        {
          date: '16.08.2025',
          time: '18:15', // Через 15 минут, но проверка аккаунтов должна быть за 30 минут (уже прошла)
          raidType: 'MO Heroic',
          status: 'открыт'
        }
      ];

      const now = new Date('2025-08-16T17:50:00Z'); // 19:50 CET - время проверки (17:45) уже прошло

      await raidMonitor.setupDynamicAccountsChecks(futureRaids, now);

      // Не должно создаваться задач, так как время проверки уже прошло
      expect(cron.schedule).not.toHaveBeenCalled();
      expect(raidMonitor.dynamicCronJobs.size).toBe(0);
    });

    test('should group multiple raids at same time into single cron job', async () => {
      const futureRaids = [
        {
          date: '16.08.2025',
          time: '21:00',
          raidType: 'MO Heroic',
          status: 'открыт'
        },
        {
          date: '16.08.2025',
          time: '21:00',
          raidType: 'LoU Normal',
          status: 'открыт'
        },
        {
          date: '16.08.2025',
          time: '21:00',
          raidType: 'MO Single Boss HC',
          status: 'открыт'
        }
      ];

      const now = new Date('2025-08-16T18:00:00Z'); // 20:00 CET

      await raidMonitor.setupDynamicAccountsChecks(futureRaids, now);

      // Проверяем, что создана только одна задача для всех рейдов на 21:00
      expect(cron.schedule).toHaveBeenCalledTimes(1);
      expect(raidMonitor.dynamicCronJobs.size).toBe(1);

      // Проверяем ключ задачи
      const jobKeys = Array.from(raidMonitor.dynamicCronJobs.keys());
      expect(jobKeys).toContain('accounts_16.08.2025|21:00');
    });
  });

  describe('performAccountsCheckForTime', () => {
    test('should send notification for time with accounts from multiple raid types', async () => {
      const clientsWithAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: 'Player1',
          team: 'h2s'
        },
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'LoU Normal',
          accounts: 'Player2',
          team: 'a2s'
        },
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Single Boss HC',
          accounts: 'Player3',
          team: 'h1'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithAccounts);

      await raidMonitor.performAccountsCheckForTime('16.08.2025', '20:00');

      // Проверяем, что уведомление было отправлено с суммарным количеством клиентов
      expect(raidMonitor.notificationService.sendNotification).toHaveBeenCalledWith(
        expect.stringContaining('не забудьте пошарить 3 аккаунтов')
      );

      // Проверяем, что уведомление помечено как отправленное
      expect(raidMonitor.notifiedStore.markAsNotified).toHaveBeenCalledWith('accounts_16.08.2025_20:00');
    });

    test('should not send notification if no clients with accounts for time', async () => {
      const clientsWithoutAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: '', // Пустое поле аккаунтов
          team: 'h2s'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithoutAccounts);

      await raidMonitor.performAccountsCheckForTime('16.08.2025', '20:00');

      // Проверяем, что уведомление НЕ было отправлено
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
      expect(raidMonitor.notifiedStore.markAsNotified).not.toHaveBeenCalled();
    });

    test('should not send duplicate notifications for same time', async () => {
      // Мокаем, что уведомление уже было отправлено
      raidMonitor.notifiedStore.isNotified.mockReturnValue(true);

      await raidMonitor.performAccountsCheckForTime('16.08.2025', '20:00');

      // Проверяем, что уведомление НЕ было отправлено повторно
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
    });
  });

  describe('performAccountsCheckForRaid', () => {
    test('should send notification for raid with accounts', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      const clientsWithAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: 'Player1',
          team: 'h2s'
        },
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: 'Player2',
          team: 'h2s'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithAccounts);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление было отправлено
      expect(raidMonitor.notificationService.sendNotification).toHaveBeenCalledWith(
        expect.stringContaining('не забудьте пошарить 2 аккаунтов')
      );
      
      // Проверяем, что уведомление помечено как отправленное
      expect(raidMonitor.notifiedStore.markAsNotified).toHaveBeenCalledWith('accounts_16.08.2025_20:00');
    });

    test('should not send notification if no clients with accounts', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      const clientsWithoutAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: '', // Пустое поле аккаунтов
          team: 'h2s'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithoutAccounts);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление НЕ было отправлено
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
      expect(raidMonitor.notifiedStore.markAsNotified).not.toHaveBeenCalled();
    });

    test('should not send duplicate notifications', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      // Мокаем, что уведомление уже было отправлено
      raidMonitor.notifiedStore.isNotified.mockReturnValue(true);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление НЕ было отправлено повторно
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
    });
  });
});
