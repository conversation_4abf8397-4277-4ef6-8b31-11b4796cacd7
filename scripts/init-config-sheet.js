#!/usr/bin/env node

/**
 * Скрипт для инициализации таблицы конфигурации в Google Sheets
 */

require('dotenv').config();
const ConfigSheetsService = require('../src/services/configSheetsService');

async function initConfigSheet() {
  try {
    console.log('🚀 Инициализация таблицы конфигурации...');
    
    const configService = new ConfigSheetsService();
    await configService.initialize();
    
    console.log('📝 Создание конфигурации по умолчанию...');
    await configService.initializeConfigSheet();
    
    console.log('✅ Таблица конфигурации успешно инициализирована!');
    console.log('🔗 Ссылка на таблицу: https://docs.google.com/spreadsheets/d/1ejznOYfsoPsSAyjy5esgfmLpzyyM9b4CGE1b3oO-BKY/edit#gid=0');
    console.log('📋 Не забудьте создать лист "config" в таблице, если его нет');
    
  } catch (error) {
    console.error('❌ Ошибка инициализации:', error.message);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  initConfigSheet();
}

module.exports = { initConfigSheet };
