# 🌐 Руководство по веб-интерфейсу WoW Raid Bot

## Обзор

Веб-интерфейс позволяет управлять всеми настройками бота через удобный браузерный интерфейс без необходимости редактирования кода.

## Быстрый старт

### 1. Локальный запуск

```bash
# Установите переменные окружения в .env
MODE=web
ADMIN_PASSWORD=your_secure_password
SESSION_SECRET=your_secret_key

# Запустите веб-интерфейс
npm run start:web
```

Откройте браузер: `http://localhost:3000/admin`

### 2. Развертывание на Vercel

1. Форкните репозиторий на GitHub
2. Подключите репозиторий к Vercel
3. Установите переменные окружения в Vercel:
   - `MODE=web`
   - `ADMIN_PASSWORD=your_secure_password`
   - `SESSION_SECRET=your_secret_key`
4. Деплойте проект

## Возможности интерфейса

### 🔧 Настройки мониторинга
- **Интервал проверки**: Как часто бот проверяет изменения (1-60 минут)
- **Задержка уведомлений о мификах**: Через сколько минут отправлять уведомления о новых мифических рейдах
- **Проверка команд до рейда**: За сколько минут до рейда проверять составы команд
- **Минимальный порог клиентов**: При каком количестве клиентов отправлять уведомления о заполненности

### 🎯 Допустимые типы рейдов
JSON конфигурация, определяющая какие типы рейдов в таблице клиентов соответствуют типам в расписании:

```json
{
  "LoU Heroic": [
    "LoU Heroic", 
    "LoU Heroic Unsaved",
    "Gallywix Heroic",
    "LoU Single Boss HC"
  ]
}
```

### 👥 Лимиты заполненности команд
Настройка максимального количества клиентов для каждой команды по сложности:

```json
{
  "h2s": {
    "normal": 20,
    "heroic": 17
  },
  "a2s": {
    "normal": 21,
    "heroic": 13
  }
}
```

### 🏆 Типы рейдов "Ласт босс"
Список типов рейдов, которые считаются рейдами последнего босса:

```json
[
  "Gallywix Heroic",
  "Gallywix Normal", 
  "Gallywix Mythic"
]
```

## Безопасность

- Доступ защищен паролем администратора
- Сессии имеют ограниченное время жизни (24 часа)
- Все изменения логируются
- JSON валидация предотвращает некорректные конфигурации

## Применение изменений

После сохранения конфигурации через веб-интерфейс:

1. **Локальный бот**: Перезапустите бота для применения изменений
2. **Vercel + локальный бот**: Изменения применятся автоматически при следующем запуске бота

## Горячие клавиши

- `Ctrl + S` - Сохранить конфигурацию
- `Ctrl + R` - Перезагрузить конфигурацию с сервера

## Режимы работы

```bash
# Только бот
npm run start:bot

# Только веб-интерфейс  
npm run start:web

# Бот + веб-интерфейс одновременно
npm run start:both
```

## Устранение неполадок

### Проблема: Не могу войти в админ-панель
- Проверьте переменную `ADMIN_PASSWORD` в .env
- Убедитесь, что используете правильный пароль

### Проблема: Изменения не применяются
- Перезапустите бота после изменения конфигурации
- Проверьте логи на наличие ошибок валидации

### Проблема: Ошибка JSON формата
- Используйте валидный JSON синтаксис
- Проверьте закрывающие скобки и кавычки
- Интерфейс автоматически форматирует JSON при потере фокуса

## Примеры конфигураций

### Базовая конфигурация для новой команды
```json
{
  "teamCapacityLimits": {
    "new_team": {
      "normal": 20,
      "heroic": 15
    }
  }
}
```

### Добавление нового типа рейда
```json
{
  "allowedRaidTypes": {
    "New Raid Type": [
      "New Raid Type",
      "New Raid Type Heroic",
      "New Raid Type Normal"
    ]
  }
}
```
