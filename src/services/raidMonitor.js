const cron = require('node-cron');
const { config } = require('../config/config');
const SheetsService = require('./sheetsService');
const ScheduleSheetsService = require('./scheduleSheetsService');
const InternalScheduleService = require('./internalScheduleService');
const DynamicConfigService = require('./dynamicConfigService');
const DateUtils = require('../utils/dateUtils');
const RaidUtils = require('../utils/raidUtils');
const NotifiedStore = require('./notifiedStore');
const crypto = require('crypto');

/**
 * Сервис для мониторинга рейдов
 */
class RaidMonitor {
  constructor(notificationService) {
    this.sheetsService = new SheetsService();
    this.scheduleService = new ScheduleSheetsService();
    this.internalScheduleService = new InternalScheduleService();
    this.dynamicConfigService = new DynamicConfigService();
    this.notificationService = notificationService;
    this.previousClients = [];
    this.mythicRaidTimers = new Map(); // Хранение таймеров для мифических рейдов
    this.dynamicCronJobs = new Map(); // Для хранения динамических cron-задач
    this.mythicNotificationSent = new Set(); // Отслеживание отправленных уведомлений
    this.initialized = false;
    this.ignoredOrderIds = new Set(); // Игнорируемые номера заказов

    // Персистентный кэш отправленных уведомлений (навсегда)
    this.notifiedStore = new NotifiedStore();
    this.isWarmStarted = false; // после старта первый проход греть кэш
  }

  /**
   * Стабильный уникальный ключ заказа: orderId, либо отпечаток содержимого строки
   */
  makeStableKey(client) {
    // 1) Предпочитаем значение из ячейки F
    let fValue = '';
    try {
      if (Array.isArray(client.rawData) && client.rawData.length > 5) {
        fValue = (client.rawData[5] || '').toString().trim();
      }
    } catch (_) {}
    if (fValue) {
      return `f:${fValue}`;
    }

    // 2) F пуст — используем устойчивый отпечаток только критичных полей
    // (дата/время/тип рейда), чтобы изменения ника/состава не влияли на ключ
    const raw = `${client.date || ''}|${client.time || ''}|${client.raidType || ''}`;
    const hash = crypto.createHash('sha1').update(raw).digest('hex').slice(0, 20);
    return `rowfp:${hash}`;
  }

  makeContentSignature(client) {
    // Подпись необходимых для сравнения полей (включая F если заполнено)
    let fValue = '';
    try {
      if (Array.isArray(client.rawData) && client.rawData.length > 5) {
        fValue = (client.rawData[5] || '').toString().trim();
      }
    } catch (_) {}
    const raw = `${fValue}|${client.date || ''}|${client.time || ''}`;
    return crypto.createHash('sha1').update(raw).digest('hex').slice(0, 16);
  }

  /**
   * Инициализация сервиса
   */
  async initialize() {
    try {
      await this.sheetsService.initialize();
      await this.scheduleService.initialize();
      await this.internalScheduleService.initialize();
      await this.dynamicConfigService.initialize();
      await this.notifiedStore.initialize();
      this.initialized = true;
      console.log('RaidMonitor успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации RaidMonitor:', error.message);
      throw error;
    }
  }

  /**
   * Основная функция проверки (вызывается каждые 10 минут)
   */
  async performCheck() {
    if (!this.initialized) {
      console.error('RaidMonitor не инициализирован');
      return;
    }

    try {
      console.log('Начинаем проверку рейдов...');

      // Получаем данные
      const [clients, schedule] = await Promise.all([
        this.sheetsService.getClientsData(),
        this.scheduleService.getCombinedScheduleData(),
      ]);

      console.log(`Получено ${clients.length} записей клиентов`);

      // Очищаем старые уведомления о мифических рейдах (в начале нового дня)
      this.cleanupOldMythicNotifications();

      // Собираем все уведомления для группировки
      const notifications = [];

      // 1. Проверяем новые мифические рейды (отправляются отдельно с таймером)
      await this.checkNewMythicRaids(clients);

      // 1.1. Проверяем новые заказы с датой в прошлом относительно бизнес-дня (граница 05:00 CET)
      const pastDateIssues = this.isWarmStarted ? this.checkNewPastDateOrders(clients) : [];
      if (pastDateIssues.length > 0) {
        notifications.push(this.formatPastDateOrdersNotification(pastDateIssues));
      }

      // 1.2. Проверяем заказы, время которых было 3 часа и более назад (по ячейкам B/T)
      const pastTimeIssues = this.isWarmStarted ? this.checkOldOrdersByTime(clients, 180) : [];
      if (pastTimeIssues.length > 0) {
        notifications.push(this.formatPastTimeOrdersNotification(pastTimeIssues));
      }

      // 2. Проверяем заполненность рейдов
      const capacityIssues = await this.checkRaidCapacity(clients, schedule);
      if (capacityIssues.length > 0) {
        notifications.push(this.formatCapacityNotification(capacityIssues));
      }

      // 3. Проверяем соответствие типов рейдов
      const typeMismatches = await this.checkRaidTypeMismatches(clients, schedule);
      if (typeMismatches.length > 0) {
        notifications.push(this.formatTypeMismatchNotification(typeMismatches));
      }

      // 4. Проверяем команды за 10 минут до рейда (старая система - оставляем как fallback)
      const teamIssues = await this.checkPreRaidTeams(clients, schedule);
      if (teamIssues.length > 0) {
        notifications.push(this.formatTeamIssuesNotification(teamIssues));
      }

      // 5. Проверяем соответствие команд клиентов командам в расписании (постоянная проверка)
      const teamMismatchIssues = await this.checkAllTeamMismatches(clients, schedule);
      if (teamMismatchIssues.length > 0) {
        notifications.push(this.formatTeamMismatchNotification(teamMismatchIssues));
      }

      // 6. Обновляем динамические cron-задачи для проверки команд
      await this.setupDynamicTeamChecks();

      // Первый проход после запуска: фиксируем базовый срез всех текущих строк как уже "уведомлённые"
      if (!this.isWarmStarted) {
        const baseKeys = clients.map(c => this.makeStableKey(c));
        const seenMap = Object.fromEntries(clients.map(c => [this.makeStableKey(c), this.makeContentSignature(c)]));
        if (baseKeys.length > 0) {
          this.notifiedStore.bulkAdd('pastDate', baseKeys);
          this.notifiedStore.bulkAdd('pastTime', baseKeys);
          this.notifiedStore.bulkSetSeen('pastDate', seenMap);
          this.notifiedStore.bulkSetSeen('pastTime', seenMap);
        }
        this.isWarmStarted = true;
      }

      // Отправляем сгруппированные уведомления одним сообщением
      if (notifications.length > 0) {
        const combinedMessage = notifications.join('\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n');
        await this.notificationService.sendNotification(combinedMessage);
        console.log(`Отправлено сгруппированное уведомление с ${notifications.length} проблемами`);
      }

      // Сохраняем текущие данные для следующей проверки
      this.previousClients = clients;

      console.log('Проверка рейдов завершена');
    } catch (error) {
      console.error('Ошибка при проверке рейдов:', error.message);
    }
  }

  /**
   * 1. Проверка новых мифических рейдов
   */
  async checkNewMythicRaids(clients) {
    try {
      const newMythicRaids = RaidUtils.findNewMythicRaids(clients, this.previousClients);

      for (const raid of newMythicRaids) {
        console.log(`Найден новый мифический рейд: ${RaidUtils.getRaidDescription(raid.date, raid.time, raid.raidType)}`);

        // Проверяем дату - не отправляем уведомления на прошедшие даты
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime || DateUtils.isDateInPast(raidDateTime)) {
          console.log(`Пропускаем мифический рейд с прошедшей датой: ${raid.date}`);
          continue;
        }

        // Проверяем статус рейда в расписании
        const schedule = await this.scheduleService.getCombinedScheduleData();
        const scheduleRaid = schedule.find(scheduleItem =>
          scheduleItem.date === raid.date &&
          scheduleItem.time === raid.time &&
          (scheduleItem.raidType === raid.raidType ||
           scheduleItem.raidType.toLowerCase().includes('mythic'))
        );

        // Если рейд найден в расписании и уже закрыт (кроме внутреннего расписания) - не отправляем уведомление
        if (scheduleRaid && scheduleRaid.status === 'закрыт' && scheduleRaid.source !== 'internal') {
          console.log(`Пропускаем закрытый мифический рейд: ${RaidUtils.getRaidDescription(raid.date, raid.time, raid.raidType)}`);
          continue;
        }

        // Сохраняем информацию о статусе рейда для уведомления
        raid.scheduleStatus = scheduleRaid ? scheduleRaid.status : 'не найден в расписании';
        raid.source = scheduleRaid ? scheduleRaid.source : null;

        // Создаем уникальный ключ для рейда
        const raidKey = `${raid.date}|${raid.time}|${raid.raidType}`;

        // Проверяем, не установлен ли уже таймер для этого рейда
        if (!this.mythicRaidTimers.has(raidKey)) {
          // Устанавливаем таймер на отправку уведомления через 15 минут
          const timer = setTimeout(async () => {
            // Удаляем таймер из карты
            this.mythicRaidTimers.delete(raidKey);
          }, config.monitoring.mythicNotificationDelayMinutes * 60 * 1000);

          this.mythicRaidTimers.set(raidKey, timer);
          console.log(`Установлен таймер для мифического рейда: ${raidKey}`);
        }
      }

      // Если есть новые мифические рейды, отправляем сгруппированное уведомление через задержку
      if (newMythicRaids.length > 0) {
        // Используем небольшую задержку, чтобы собрать все рейды из текущей проверки
        setTimeout(async () => {
          await this.sendGroupedMythicNotifications();
        }, 1000); // 1 секунда задержки для группировки
      }
    } catch (error) {
      console.error('Ошибка при проверке новых мифических рейдов:', error.message);
    }
  }

  /**
   * Отправка сгруппированных уведомлений о мифических рейдах
   */
  async sendGroupedMythicNotifications() {
    try {
      // Получаем текущие данные клиентов и расписание
      const [clients, schedule] = await Promise.all([
        this.sheetsService.getClientsData(),
        this.scheduleService.getCombinedScheduleData()
      ]);

      // Находим все активные мифические рейды
      const mythicRaidsToNotify = [];
      const processedRaids = new Set();

      for (const client of clients) {
        if (RaidUtils.isMythicRaid(client.raidType)) {
          const raidKey = `${client.date}|${client.time}`;

          // Избегаем дублирования
          if (!processedRaids.has(raidKey)) {
            processedRaids.add(raidKey);

            // Проверяем, что рейд не в прошлом
            const raidDateTime = DateUtils.createDateTime(client.date, client.time);
            if (raidDateTime && !DateUtils.isDateInPast(raidDateTime)) {

              // Проверяем статус рейда в расписании
              const scheduleRaid = schedule.find(scheduleItem =>
                scheduleItem.date === client.date &&
                scheduleItem.time === client.time &&
                (scheduleItem.raidType === client.raidType ||
                 scheduleItem.raidType.toLowerCase().includes('mythic'))
              );

              // Пропускаем закрытые рейды (кроме тех, что из внутреннего расписания)
              if (scheduleRaid && scheduleRaid.status === 'закрыт' && scheduleRaid.source !== 'internal') {
                continue;
              }

              // Проверяем, не отправляли ли мы уже уведомление для этого дня
              const notificationKey = `${client.date}`;
              if (!this.mythicNotificationSent.has(notificationKey)) {
                mythicRaidsToNotify.push({
                  date: client.date,
                  time: client.time,
                  raidType: client.raidType,
                  scheduleStatus: scheduleRaid ? scheduleRaid.status : 'не найден в расписании',
                  source: scheduleRaid ? scheduleRaid.source : null
                });
              }
            }
          }
        }
      }

      // Если есть мифические рейды для уведомления, отправляем сгруппированное сообщение
      if (mythicRaidsToNotify.length > 0) {
        const message = this.formatGroupedMythicNotification(mythicRaidsToNotify);
        await this.notificationService.sendNotification(message);
        console.log(`Отправлено сгруппированное уведомление о ${mythicRaidsToNotify.length} мифических рейдах`);

        // Отмечаем, что уведомления для этих дат отправлены
        mythicRaidsToNotify.forEach(raid => {
          this.mythicNotificationSent.add(raid.date);
        });
      }
    } catch (error) {
      console.error('Ошибка при отправке сгруппированных уведомлений о мифических рейдах:', error.message);
    }
  }

  /**
   * Очистка старых уведомлений о мифических рейдах
   */
  cleanupOldMythicNotifications() {
    const today = DateUtils.formatDate(DateUtils.now());

    // Удаляем уведомления для прошедших дат
    for (const date of this.mythicNotificationSent) {
      if (date < today) {
        this.mythicNotificationSent.delete(date);
      }
    }
  }

  /**
   * Проверка появления новых заказов с датой меньше текущего рабочего дня (учитывая границу 05:00 CET)
   * Отправляем по каждому такому заказу уведомление только один раз.
   */
  checkNewPastDateOrders(clients) {
    const issues = [];
    for (const client of clients) {
      const dateMoment = DateUtils.parseDate(client.date);
      if (dateMoment && DateUtils.isBeforeBusinessDay(dateMoment)) {
        const key = this.makeStableKey(client);
        // Проверяем, был ли этот ключ раньше и изменилось ли содержимое (например, дата стала меньше)
        const prevSig = this.notifiedStore.getSeen('pastDate', key);
        const currSig = this.makeContentSignature(client);
        const isChanged = prevSig && prevSig !== currSig;

        if (!this.notifiedStore.has('pastDate', key) || isChanged) {
          issues.push({ rowNumber: client.rowNumber, date: client.date });
          // Обновляем signature и помечаем как уведомлённое
          this.notifiedStore.setSeen('pastDate', key, currSig);
          this.notifiedStore.add('pastDate', key);
        }
      }
    }
    return issues;
  }

  /**
   * Форматирование уведомления для заказов на прошедшую дату
   */
  formatPastDateOrdersNotification(items) {
    if (items.length === 1) {
      const it = items[0];
      return `⚠️ ${it.rowNumber}, ${it.date} - проверьте, появился заказ на уже прошедшую дату`;
    }
    const lines = items
      .sort((a, b) => a.rowNumber - b.rowNumber)
      .map((it, idx) => `${idx + 1}. Строка ${it.rowNumber}, дата ${it.date}`)
      .join('\n');
    return `⚠️ Обнаружены заказы на прошедшие даты, проверьте:\n${lines}`;
  }

  /**
   * Проверка заказов, у которых (дата B + время T) были >= N минут назад
   * Учитывает CET/CEST, предотвращает повторные уведомления в рамках рабочего дня
   */
  checkOldOrdersByTime(clients, minutesThreshold = 180) {
    const items = [];
    for (const client of clients) {
      // Используем бизнес-временную шкалу, чтобы корректно работать с 00:00–04:59
      const dt = DateUtils.createBusinessDateTime(client.date, client.time);
      if (!dt) continue;

      const key = this.makeStableKey(client);
      
      // Проверяем, был ли заказ уже "увиден" в pastTime
      const wasSeenInPastTime = this.notifiedStore.has('pastTime', key);
      
      // Если заказ уже был в pastDate - пропускаем
      if (this.notifiedStore.has('pastDate', key)) continue;
      
      // Если заказ уже был в pastTime, проверяем, изменилось ли время
      if (wasSeenInPastTime) {
        const prevSig = this.notifiedStore.getSeen('pastTime', key);
        const currSig = this.makeContentSignature(client);
        
        // Если время не изменилось - пропускаем (это "доехавший" заказ)
        if (prevSig === currSig) continue;
        
        // Если время изменилось, но новое время НЕ подходит под уведомление - обновляем сигнатуру и пропускаем
        if (!DateUtils.hasPassedMinutes(dt, minutesThreshold)) {
          this.notifiedStore.setSeen('pastTime', key, currSig);
          continue;
        }
        // Если время изменилось на подходящее под уведомление - показываем уведомление
      }

      if (DateUtils.hasPassedMinutes(dt, minutesThreshold)) {
        items.push({ rowNumber: client.rowNumber, date: client.date, time: client.time });
        this.notifiedStore.setSeen('pastTime', key, this.makeContentSignature(client));
        this.notifiedStore.add('pastTime', key);
      }
    }

    return items.sort((a, b) => a.rowNumber - b.rowNumber);
  }

  /**
   * Форматирование уведомления для заказов, старше 3+ часов
   */
  formatPastTimeOrdersNotification(items) {
    if (items.length === 1) {
      const it = items[0];
      return `⏰ ${it.rowNumber}, ${it.date} ${it.time} - проверьте, заказ создан 3+ часа назад`;
    }
    const lines = items
      .sort((a, b) => a.rowNumber - b.rowNumber)
      .map((it, idx) => `${idx + 1}. Строка ${it.rowNumber}, ${it.date} ${it.time}`)
      .join('\n');
    return `⏰ Обнаружены заказы со временем 3+ часа назад:\n${lines}`;
  }

  /**
   * Форматирование сгруппированного уведомления о мифических рейдах
   */
  formatGroupedMythicNotification(mythicRaids) {
    if (mythicRaids.length === 1) {
      // Если только один рейд, используем обновленный формат
      const raid = mythicRaids[0];
      let statusNote = '';
      if (raid.scheduleStatus === 'не найден в расписании') {
        statusNote = ' (в расписании рейд не найден)';
      } else if (raid.scheduleStatus === 'закрыт') {
        statusNote = ' 🔒 (закрыт)';
        if (raid.source === 'internal') {
          statusNote += ' (внутреннее расписание)';
        }
      }
      return `🔥 <b>Проверь не нужно ли закрыть в расписании мифик рейд - ${raid.date} ${raid.time}${statusNote}</b>`;
    }

    // Если несколько рейдов, группируем
    let message = '🔥 <b>Проверь не нужно ли закрыть в расписании мифик рейды:</b>\n\n';

    mythicRaids.forEach((raid, index) => {
      let statusNote = '';
      if (raid.scheduleStatus === 'не найден в расписании') {
        statusNote = ' (в расписании рейд не найден)';
      } else if (raid.scheduleStatus === 'закрыт') {
        statusNote = ' 🔒 (закрыт)';
        if (raid.source === 'internal') {
          statusNote += ' (внутреннее расписание)';
        }
      }
      message += `${index + 1}. ${raid.date} ${raid.time}${statusNote}\n`;
    });

    return message;
  }

  /**
   * 2. Проверка заполненности рейдов
   */
  async checkRaidCapacity(clients, schedule) {
    const issues = [];

    try {
      console.log(`\n=== Проверка заполненности рейдов ===`);
      console.log(`Всего рейдов в расписании: ${schedule.length}`);
      
      // Исключаем Mythic рейды и ЗАКРЫТЫЕ рейды из проверки заполненности
      // Проверяем только ОТКРЫТЫЕ рейды, так как закрытые уже не принимают клиентов
      const raidsToCheck = schedule.filter(raid =>
        !raid.raidType.toLowerCase().includes('mythic') &&
        raid.status !== 'закрыт'
      );
      
      console.log(`Рейдов для проверки заполненности: ${raidsToCheck.length}`);
      console.log(`Исключено: ${schedule.length - raidsToCheck.length} рейдов (mythic + закрытые)`);
      
      const internalSchedule = await this.internalScheduleService.getInternalScheduleData();
      const clientGroups = RaidUtils.groupClientsByRaid(clients);

      // Проходим по каждому ОТКРЫТОМУ рейду в расписании
      for (const raid of raidsToCheck) {
        console.log(`Проверяем рейд на заполненность: ${raid.date} ${raid.time} ${raid.raidType} (статус: ${raid.status})`);
        
        // Получаем информацию о командах из внутреннего расписания для этого конкретного рейда
        const teamInfo = await this.getTeamInfoForRaid(raid.date, raid.time, raid.raidType, internalSchedule, raid);
        console.log(`  Команды: ${teamInfo.teams.map(t => `${t.team}:${t.limit}`).join(', ')}`);
        console.log(`  Общий лимит: ${teamInfo.totalLimit}`);

        // Получаем всех клиентов для этой команды, даты и времени (используем ту же логику, что и в /filled)
        const { totalClients, lastBossClients } = await this.getClientsForTeamTime(
          raid.date,
          raid.time,
          teamInfo.teams,
          clientGroups,
          internalSchedule
        );
        console.log(`  Клиентов: ${totalClients} (ласт босс: ${lastBossClients})`);

        // Используем только totalClients (исключаем lastBossClients из проверки заполненности)
        const effectiveClientCount = totalClients;
        const limit = teamInfo.totalLimit || RaidUtils.getDefaultRaidLimit(raid.raidType);

        if (effectiveClientCount >= limit) {
          issues.push({
            type: 'capacity',
            raidType: raid.raidType,
            date: raid.date,
            time: raid.time,
            clientCount: effectiveClientCount,
            lastBossCount: lastBossClients,
            limit: limit,
            teamDetails: teamInfo.teams,
            status: raid.status,
            source: raid.source
          });
          console.log(`  ❌ НАЙДЕН ЗАПОЛНЕННЫЙ РЕЙД: ${raid.date}|${raid.time}|${raid.raidType} (${effectiveClientCount}/${limit}, ласт: ${lastBossClients})`);
        } else {
          console.log(`  ✅ Рейд не заполнен: ${effectiveClientCount}/${limit}`);
        }
      }
      
      console.log(`\nИтого найдено заполненных рейдов: ${issues.length}`);
      if (issues.length > 0) {
        issues.forEach((issue, index) => {
          console.log(`  ${index + 1}. ${issue.raidType} ${issue.date} ${issue.time}: ${issue.clientCount}/${issue.limit}`);
        });
      }
      console.log(`=== Конец проверки заполненности ===\n`);
      
    } catch (error) {
      console.error('Ошибка при проверке заполненности рейдов:', error.message);
    }

    return issues;
  }

  /**
   * 3. Проверка соответствия типов рейдов
   */
  async checkRaidTypeMismatches(clients, schedule) {
    try {
      const clientGroups = RaidUtils.groupClientsByRaid(clients);
      const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
      const mismatches = RaidUtils.findRaidTypeMismatches(schedule, clientGroups, allowedRaidTypes, this.ignoredOrderIds);

      if (mismatches.length > 0) {
        console.log(`Найдено ${mismatches.length} несоответствий типов рейдов`);
      }

      return mismatches;
    } catch (error) {
      console.error('Ошибка при проверке соответствия типов рейдов:', error.message);
      return [];
    }
  }

  /**
   * Форсированная проверка соответствия типов рейдов (для команды /teams)
   * Проверяет рейды не старше 3 часов, сохраняет игнор-лист типов
   */
  async checkRaidTypeMismatchesForced(clients, schedule) {
    try {
      const clientGroups = RaidUtils.groupClientsByRaid(clients);
      const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
      const mismatches = RaidUtils.findRaidTypeMismatchesForced(schedule, clientGroups, allowedRaidTypes, this.ignoredOrderIds);

      if (mismatches.length > 0) {
        console.log(`Найдено ${mismatches.length} несоответствий типов рейдов (форсированная проверка)`);
      }

      return mismatches;
    } catch (error) {
      console.error('Ошибка при форсированной проверке соответствия типов рейдов:', error.message);
      return [];
    }
  }

  /**
   * Форсированная проверка заказов на прошедшую дату (с учётом границы 05:00 CET)
   */
  checkPastDateOrdersForced(clients) {
    const items = [];
    for (const client of clients) {
      const m = DateUtils.parseDate(client.date);
      if (m && DateUtils.isBeforeBusinessDay(m)) {
        const key = this.makeStableKey(client);
        // Не показываем повторно, учитываем уже зафиксированные в персистентном хранилище
        const alreadyAny = this.notifiedStore.has('pastDate', key);
        if (!alreadyAny) {
          items.push({ rowNumber: client.rowNumber, date: client.date });
          // Фиксируем сразу, чтобы при следующем /teams не повторялось
          this.notifiedStore.add('pastDate', key);
        }
      }
    }
    return items.sort((a, b) => a.rowNumber - b.rowNumber);
  }

  /**
   * 4. Проверка команд за 10 минут до рейда
   */
  async checkPreRaidTeams(clients, schedule) {
    const issues = [];

    try {
      const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
      
      for (const scheduleRaid of schedule) {
        // Создаем дату и время рейда
        const raidDateTime = DateUtils.createDateTime(scheduleRaid.date, scheduleRaid.time);

        if (!raidDateTime) {
          continue; // Пропускаем рейды с некорректными датами
        }

        // Проверяем, начинается ли рейд через 10 минут (±2 минуты для погрешности)
        const minutesUntilRaid = DateUtils.getMinutesUntil(raidDateTime);
        const checkMinutes = config.monitoring.preRaidCheckMinutes;

        if (minutesUntilRaid >= (checkMinutes - 2) && minutesUntilRaid <= (checkMinutes + 2)) {
          // Получаем клиентов для этого рейда
          const raidClients = clients.filter(client =>
            client.date === scheduleRaid.date &&
            client.time === scheduleRaid.time &&
            RaidUtils.isRaidTypeAllowed(scheduleRaid.raidType, client.raidType, allowedRaidTypes)
          );

          if (raidClients.length > 0) {
            // Проверяем команды
            const teamIssues = RaidUtils.checkTeamAssignments(scheduleRaid, raidClients, this.ignoredOrderIds);

            if (teamIssues.length > 0) {
              issues.push({
                type: 'team',
                raidType: scheduleRaid.raidType,
                date: scheduleRaid.date,
                time: scheduleRaid.time,
                teamIssues: teamIssues,
                status: scheduleRaid.status,
                source: scheduleRaid.source
              });
              console.log(`Найдены проблемы с командами для рейда: ${RaidUtils.getRaidDescription(scheduleRaid.date, scheduleRaid.time, scheduleRaid.raidType)}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Ошибка при проверке команд перед рейдом:', error.message);
    }

    return issues;
  }

  /**
   * Форматирование уведомления о заполненности рейдов
   */
  formatCapacityNotification(issues) {
    const header = '🔥 <b>Рейды заполнены!</b>';
    const items = issues.map(issue => {
      let text = `• ${issue.raidType} ${issue.date} ${issue.time} (${issue.clientCount}`;

      if (issue.limit) {
        text += `/${issue.limit}`;
      }

      text += ' клиентов)';

      if (issue.teamDetails && issue.teamDetails.length > 0) {
        const teamInfo = issue.teamDetails.map(team => `${team.team}: ${team.limit}`).join(', ');
        text += `\n  Составы: ${teamInfo}`;
      }

      return text;
    });
    return `${header}\n\n${items.join('\n\n')}`;
  }

  /**
   * Форматирование уведомления о несоответствии типов рейдов
   */
  formatTypeMismatchNotification(mismatches) {
    const header = '⚠️ <b>Несоответствие типов рейдов!</b>';
    const items = mismatches.map(mismatch => {
      let text = `• ${mismatch.date} ${mismatch.time}: расписание "${mismatch.scheduleRaidType}" ≠ клиенты "${mismatch.clientRaidType}"`;
      if (Array.isArray(mismatch.clientsDetailed) && mismatch.clientsDetailed.length > 0) {
        const lines = mismatch.clientsDetailed
          .sort((a, b) => (a.rowNumber || 0) - (b.rowNumber || 0))
          .map(cd => {
            const orderInfo = cd.orderId ? `Заказ: "${cd.orderId}" ` : '';
            const typeInfo = cd.clientRaidType ? `тип рейда "${cd.clientRaidType}"` : 'тип рейда клиента';
            return `  ❌ Строка ${cd.rowNumber}: ${orderInfo}${typeInfo} не соответствует расписанию`;
          });
        text += `\n${lines.join('\n')}`;
      }
      return text;
    });
    return `${header}\n\n${items.join('\n')}`;
  }

  /**
   * Форматирование уведомления о проблемах с командами
   */
  formatTeamIssuesNotification(issues) {
    const header = '👥 <b>Проблемы с командами!</b>';
    const items = issues.map(issue => {
      let text = `• ${issue.raidType} ${issue.date} ${issue.time}:`;
      
      // Добавляем информацию о статусе и источнике
      if (issue.status === 'закрыт') {
        text += ' 🔒';
        if (issue.source === 'internal') {
          text += ' (внутреннее расписание)';
        }
      }
      
      text += `\n  ${issue.teamIssues.join('\n  ')}`;
      return text;
    });
    return `${header}\n\n${items.join('\n\n')}`;
  }

  /**
   * Получение лимитов команд для рейда из внутреннего расписания
   */
  async getTeamLimitsForRaid(date, time, raidType, internalSchedule) {
    const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
    const teamCapacityLimits = await this.dynamicConfigService.getTeamCapacityLimits();
    
    // Находим все команды для этого рейда
    const teamsForRaid = internalSchedule.filter(raid =>
      raid.date === date &&
      raid.time === time &&
      RaidUtils.isRaidTypeAllowed(raid.raidType, raidType, allowedRaidTypes)
    );

    // Определяем сложность рейда
    const difficulty = this.getRaidDifficulty(raidType);

    // Суммируем лимиты всех команд
    let totalLimit = 0;
    const teamDetails = [];

    teamsForRaid.forEach(raid => {
      const teamLimits = teamCapacityLimits?.[raid.team];
      if (teamLimits && teamLimits[difficulty]) {
        const limit = teamLimits[difficulty];
        totalLimit += limit;
        teamDetails.push({
          team: raid.team,
          limit: limit
        });
      }
    });

    return {
      totalLimit,
      teamDetails,
      difficulty
    };
  }

  /**
   * Определение сложности рейда по названию (без mythic)
   */
  getRaidDifficulty(raidType) {
    const raidTypeLower = raidType.toLowerCase();

    if (raidTypeLower.includes('heroic')) {
      return 'heroic';
    } else if (raidTypeLower.includes('normal')) {
      return 'normal';
    }

    // По умолчанию считаем normal
    return 'normal';
  }

  /**
   * Получение статистики заполненности рейдов начиная с сегодняшнего дня
   */
  async getRaidCapacityStats() {
    try {
      const clients = await this.sheetsService.getClientsData();
      const schedule = await this.scheduleService.getCombinedScheduleData();
      const internalSchedule = await this.internalScheduleService.getInternalScheduleData();

      const clientGroups = RaidUtils.groupClientsByRaid(clients);
      const today = new Date();
      const todayStr = this.formatDateForComparison(today);

      const stats = [];

      // Проходим по всем рейдам в расписании начиная с сегодня, исключая Mythic
      // Включаем как открытые, так и закрытые рейды для полной информации
      const futureRaids = schedule.filter(raid => {
        const raidDateStr = this.formatDateForComparison(this.parseDisplayDate(raid.date));
        const isMythic = raid.raidType.toLowerCase().includes('mythic');
        return raidDateStr >= todayStr && !isMythic;
      });

      // Сортируем по дате и времени
      futureRaids.sort((a, b) => {
        const dateComparison = a.date.localeCompare(b.date);
        if (dateComparison !== 0) return dateComparison;
        return a.time.localeCompare(b.time);
      });

      // Обрабатываем каждый рейд отдельно, но с правильным подсчетом клиентов по командам
      for (const raid of futureRaids) {
        // Получаем информацию о командах из внутреннего расписания для этого конкретного рейда
        const teamInfo = await this.getTeamInfoForRaid(raid.date, raid.time, raid.raidType, internalSchedule, raid);

        // Получаем всех клиентов для этой команды, даты и времени (не только для этого рейда)
        const { totalClients, lastBossClients } = await this.getClientsForTeamTime(
          raid.date,
          raid.time,
          teamInfo.teams,
          clientGroups,
          internalSchedule
        );

        // Добавляем статистику для этого рейда
        stats.push({
          date: raid.date,
          time: raid.time,
          raidType: raid.raidType,
          clientCount: totalClients,
          lastBossCount: lastBossClients,
          status: raid.status === 'закрыт' ? 'закрыт' : 'открыт',
          teams: teamInfo.teams,
          totalLimit: teamInfo.totalLimit
        });
      }



      return stats;
    } catch (error) {
      console.error('Ошибка получения статистики заполненности:', error.message);
      throw error;
    }
  }

  /**
   * Получение клиентов для команды, даты и времени
   */
    async getClientsForTeamTime(date, time, teams, clientGroups, internalSchedule) {
    // Получаем динамическую конфигурацию
    const lastBossRaidTypes = await this.dynamicConfigService.getLastBossRaidTypes();
    
    // Если нет команд, используем простую логику
    if (teams.length === 0) {
      const matchingClients = Object.entries(clientGroups).filter(([, clientGroup]) => {
        return clientGroup.date === date && clientGroup.time === time;
      });

      let totalClients = 0;
      let lastBossClients = 0;

      matchingClients.forEach(([, clientGroup]) => {
        if (lastBossRaidTypes?.includes(clientGroup.raidType)) {
          lastBossClients += clientGroup.clientCount;
        } else {
          totalClients += clientGroup.clientCount;
        }
      });

      return { totalClients, lastBossClients };
    }

    // Получаем уникальные команды
    const uniqueTeams = [...new Set(teams.map(t => t.team))];

    // Получаем ВСЕ рейды этих команд в это время из внутреннего расписания
    const allTeamRaids = internalSchedule.filter(raid =>
      raid.date === date &&
      raid.time === time &&
      uniqueTeams.includes(raid.team)
    );

    // Ищем всех клиентов, которые подходят к ЛЮБОМУ рейду этих команд
    const allMatchingClients = Object.entries(clientGroups).filter(([, clientGroup]) => {
      return clientGroup.date === date &&
             clientGroup.time === time &&
             // Проверяем, подходит ли тип рейда клиентов к любому рейду команды в это время
             allTeamRaids.some(teamRaid =>
               RaidUtils.isRaidTypeAllowed(teamRaid.raidType, clientGroup.raidType)
             );
    });

    // Разделяем клиентов на обычных и "ласт босса"
    let totalClients = 0;
    let lastBossClients = 0;

    allMatchingClients.forEach(([, clientGroup]) => {
      if (lastBossRaidTypes?.includes(clientGroup.raidType)) {
        lastBossClients += clientGroup.clientCount;
      } else {
        totalClients += clientGroup.clientCount;
      }
    });

    return { totalClients, lastBossClients };
  }



  /**
   * Получение информации о командах для рейда
   */
  async getTeamInfoForRaid(date, time, raidType, internalSchedule, scheduleRaid = null) {
    const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
    const teamCapacityLimits = await this.dynamicConfigService.getTeamCapacityLimits();
    
    // Если у нас есть информация о рейде из расписания и он имеет команды
    if (scheduleRaid && scheduleRaid.teams && scheduleRaid.source === 'internal') {
      const difficulty = this.getRaidDifficulty(raidType);
      let totalLimit = 0;
      const teams = [];

      scheduleRaid.teams.forEach(teamName => {
        const teamLimits = teamCapacityLimits?.[teamName];
        if (teamLimits && teamLimits[difficulty]) {
          const limit = teamLimits[difficulty];
          totalLimit += limit;
          teams.push({
            team: teamName,
            limit: limit
          });
        }
      });

      if (teams.length > 0) {
        return { teams, totalLimit };
      }
    }
    
    // Старая логика для рейдов из внешнего расписания
    // Сначала ищем точное совпадение
    let teamsForRaid = internalSchedule.filter(raid =>
      raid.date === date &&
      raid.time === time &&
      raid.raidType === raidType
    );

    // Если точного совпадения нет, ищем по allowedRaidTypes
    if (teamsForRaid.length === 0) {
      teamsForRaid = internalSchedule.filter(raid =>
        raid.date === date &&
        raid.time === time &&
        RaidUtils.isRaidTypeAllowed(raid.raidType, raidType, allowedRaidTypes)
      );
    }

    const difficulty = this.getRaidDifficulty(raidType);
    let totalLimit = 0;
    const teams = [];
    const seenTeams = new Set(); // Для избежания дубликатов

    teamsForRaid.forEach(raid => {
      const teamLimits = teamCapacityLimits?.[raid.team];
      if (teamLimits && teamLimits[difficulty]) {
        const limit = teamLimits[difficulty];

        // Добавляем лимит и команду только если её еще нет
        if (!seenTeams.has(raid.team)) {
          totalLimit += limit;
          teams.push({
            team: raid.team,
            limit: limit
          });
          seenTeams.add(raid.team);
        }
      }
    });

    // Если нет команд во внутреннем расписании, используем лимит по умолчанию
    if (teams.length === 0) {
      totalLimit = RaidUtils.getDefaultRaidLimit(raidType);
    }

    return { teams, totalLimit };
  }

  /**
   * Парсинг даты из формата отображения DD.MM.YYYY
   */
  parseDisplayDate(dateStr) {
    const [day, month, year] = dateStr.split('.');
    return new Date(year, month - 1, day);
  }

  /**
   * Проверка соответствия клиентов команде и времени
   */
  async isClientMatchingTeamTime(clientGroup, date, time, internalSchedule) {
    const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
    
    // Находим все команды для этой даты и времени во внутреннем расписании
    const teamsForTime = internalSchedule.filter(raid =>
      raid.date === date && raid.time === time
    );

    // Проверяем, подходит ли тип рейда клиентов к любому рейду этих команд
    return teamsForTime.some(internalRaid =>
      RaidUtils.isRaidTypeAllowed(internalRaid.raidType, clientGroup.raidType, allowedRaidTypes)
    );
  }

  /**
   * Форматирование даты для сравнения (YYYY-MM-DD)
   */
  formatDateForComparison(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Создание динамических cron-задач для проверки команд перед рейдами
   */
  async setupDynamicTeamChecks() {
    try {
      // Получаем расписание
      const schedule = await this.scheduleService.getCombinedScheduleData();
      const now = DateUtils.now();

      console.log(`Текущее время: ${DateUtils.formatDateTime(now)}`);

      // Очищаем старые задачи
      for (const [key, job] of this.dynamicCronJobs) {
        job.stop();
        job.destroy();
      }
      this.dynamicCronJobs.clear();

      // Создаем задачи для рейдов на ближайшие 7 дней (включая закрытые - в них тоже есть клиенты)
      const futureRaids = schedule.filter(raid => {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) return false;

        // Рейды на ближайшие 7 дней (включая закрытые)
        const daysDiff = raidDateTime.diff(now, 'days');
        return daysDiff >= 0 && daysDiff <= 7;
      });

      console.log(`Создание динамических задач для ${futureRaids.length} рейдов`);

      // Показываем все рейды для отладки
      futureRaids.forEach(raid => {
        console.log(`- Рейд: ${raid.date} ${raid.time} ${raid.raidType} (статус: ${raid.status})`);
      });

      for (const raid of futureRaids) {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) {
          console.log(`Пропускаем рейд с некорректной датой/временем: ${raid.date} ${raid.time}`);
          continue;
        }

        // Время проверки: за 10 минут до рейда
        const checkTime = raidDateTime.clone().subtract(config.monitoring.preRaidCheckMinutes, 'minutes');

        // Проверяем, что время проверки еще не прошло
        if (checkTime.isBefore(now)) {
          console.log(`Пропускаем рейд ${raid.date} ${raid.time} ${raid.raidType} (статус: ${raid.status}) - время проверки уже прошло: ${DateUtils.formatDateTime(checkTime)}`);
          continue;
        }

        // Создаем cron-выражение для точного времени
        const cronExpression = `${checkTime.minutes()} ${checkTime.hours()} ${checkTime.date()} ${checkTime.month() + 1} *`;
        const jobKey = `${raid.date}|${raid.time}|${raid.raidType}`;

        try {
          const job = cron.schedule(cronExpression, async () => {
            console.log(`Выполняется динамическая проверка команд для рейда: ${jobKey}`);
            await this.performTeamCheckForRaid(raid);

            // Удаляем задачу после выполнения
            job.stop();
            job.destroy();
            this.dynamicCronJobs.delete(jobKey);
          }, {
            scheduled: false,
            timezone: config.timezone,
          });

          this.dynamicCronJobs.set(jobKey, job);
          job.start();

          console.log(`Создана динамическая задача для рейда ${jobKey} (статус: ${raid.status}) на ${DateUtils.formatDateTime(checkTime)}`);
        } catch (error) {
          console.error(`Ошибка создания cron-задачи для рейда ${jobKey}:`, error.message);
        }
      }

      console.log(`Создано ${this.dynamicCronJobs.size} динамических cron-задач`);

      // Дополнительная отладочная информация
      if (this.dynamicCronJobs.size < futureRaids.length) {
        console.log(`Внимание: создано задач (${this.dynamicCronJobs.size}) меньше чем рейдов (${futureRaids.length})`);
        console.log('Возможные причины: время проверки уже прошло, ошибки создания cron-выражений');
      }
    } catch (error) {
      console.error('Ошибка при создании динамических cron-задач:', error.message);
    }
  }

  /**
   * Проверка соответствия команд для всех рейдов (основной цикл мониторинга)
   */
  async checkAllTeamMismatches(clients, schedule) {
    try {
      const internalSchedule = await this.internalScheduleService.getInternalScheduleData();
      const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();
      const allMismatches = [];

      // Проверяем все рейды на сегодня и завтра (включая закрытые)
      const now = DateUtils.now();
      const relevantRaids = schedule.filter(raid => {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) return false;

        const daysDiff = raidDateTime.diff(now, 'days');
        return daysDiff >= 0 && daysDiff <= 1; // Убираем фильтр по статусу
      });

      for (const raid of relevantRaids) {
        // Получаем клиентов для этого рейда
        const raidClients = clients.filter(client =>
          client.date === raid.date &&
          client.time === raid.time &&
          RaidUtils.isRaidTypeAllowed(raid.raidType, client.raidType, allowedRaidTypes)
        );

        if (raidClients.length > 0) {
          const mismatches = await this.checkTeamMismatches(raidClients, internalSchedule, raid);
          allMismatches.push(...mismatches);
        }
      }

      return allMismatches;
    } catch (error) {
      console.error('Ошибка при проверке соответствия команд:', error.message);
      return [];
    }
  }

  /**
   * Проверка соответствия команд клиентов командам во внутреннем расписании
   */
  async checkTeamMismatches(clients, internalSchedule, raid) {
    const mismatches = [];
    const allowedRaidTypes = await this.dynamicConfigService.getAllowedRaidTypes();

    // Получаем команды из внутреннего расписания для этого рейда
    const expectedTeams = internalSchedule.filter(scheduleRaid =>
      scheduleRaid.date === raid.date &&
      scheduleRaid.time === raid.time &&
      RaidUtils.isRaidTypeAllowed(scheduleRaid.raidType, raid.raidType, allowedRaidTypes)
    ).map(scheduleRaid => scheduleRaid.team);

    if (expectedTeams.length === 0) {
      // Если во внутреннем расписании нет команд для этого рейда, это не ошибка
      return mismatches;
    }

    // Проверяем каждого клиента
    for (const client of clients) {
      // Игнор по orderId
      if (client.orderId && this.ignoredOrderIds.has(client.orderId)) {
        continue;
      }
      if (!client.team || client.team.trim() === '') {
        // Пустые команды проверяются отдельно
        continue;
      }

      const clientTeam = RaidUtils.normalizeTeamName(client.team);

      // Игнорируем команду "DEP"
      if (clientTeam === 'dep') {
        continue;
      }

      const hasMatchingTeam = expectedTeams.some(expectedTeam =>
        RaidUtils.normalizeTeamName(expectedTeam) === clientTeam
      );

      if (!hasMatchingTeam) {
        mismatches.push({
          type: 'team_mismatch',
          raidType: raid.raidType,
          date: raid.date,
          time: raid.time,
          clientTeam: client.team,
          orderId: client.orderId,
          expectedTeams: expectedTeams,
          rowNumber: client.rowNumber,
          status: raid.status,
          source: raid.source
        });
      }
    }

    return mismatches;
  }

  /**
   * Форматирование уведомления о несоответствии команд
   */
  formatTeamMismatchNotification(mismatches) {
    if (mismatches.length === 0) return '';

    // Группируем по рейдам
    const groupedMismatches = {};
    for (const mismatch of mismatches) {
      const key = `${mismatch.date}|${mismatch.time}|${mismatch.raidType}`;
      if (!groupedMismatches[key]) {
        groupedMismatches[key] = {
          raid: mismatch,
          clients: []
        };
      }
      groupedMismatches[key].clients.push(mismatch);
    }

    const messages = [];
    for (const [, group] of Object.entries(groupedMismatches)) {
      const { raid, clients } = group;

      let message = `⚠️ <b>Неправильные команды!</b>\n`;
      message += `📅 <b>${raid.raidType}</b>\n`;
      message += `🗓 <b>${raid.date} ${raid.time}</b>`;
      
      // Добавляем информацию о статусе и источнике
      if (raid.status === 'закрыт') {
        message += ' 🔒';
        if (raid.source === 'internal') {
          message += ' (внутреннее расписание)';
        }
      }
      
      message += '\n\n';

      message += `<b>Ожидаемые команды:</b> ${raid.expectedTeams.join(', ')}\n\n`;
      message += `<b>Проблемы:</b>\n`;

      for (const client of clients) {
        const orderInfo = client.orderId ? `Заказ: "${client.orderId}" ` : '';
        message += `❌ Строка ${client.rowNumber}: ${orderInfo}команда "${client.clientTeam}" не соответствует расписанию\n`;
      }

      messages.push(message);
    }

    return messages.join('\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n');
  }

  /**
   * Выполнение проверки команд для конкретного рейда
   */
  async performTeamCheckForRaid(raid) {
    try {
      const clients = await this.sheetsService.getClientsData();
      const internalSchedule = await this.internalScheduleService.getInternalScheduleData();

      // Получаем клиентов для этого рейда
      const raidClients = clients.filter(client =>
        client.date === raid.date &&
        client.time === raid.time &&
        RaidUtils.isRaidTypeAllowed(raid.raidType, client.raidType)
      );

      if (raidClients.length === 0) {
        console.log(`Нет клиентов для рейда ${raid.date} ${raid.time} ${raid.raidType}`);
        return;
      }

      const notifications = [];

      // 1. Проверяем пустые команды
      const emptyTeamIssues = RaidUtils.checkTeamAssignments(raid, raidClients);
      if (emptyTeamIssues.length > 0) {
        notifications.push(this.formatTeamIssuesNotification([{
          type: 'team',
          raidType: raid.raidType,
          date: raid.date,
          time: raid.time,
          teamIssues: emptyTeamIssues,
          status: raid.status,
          source: raid.source
        }]));
      }

      // 2. Проверяем правильность команд (новая функция)
      const teamMismatches = await this.checkTeamMismatches(raidClients, internalSchedule, raid);
      if (teamMismatches.length > 0) {
        notifications.push(this.formatTeamMismatchNotification(teamMismatches));
      }

      // Отправляем уведомления
      if (notifications.length > 0) {
        const combinedMessage = notifications.join('\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n');
        await this.notificationService.sendNotification(combinedMessage);
        console.log(`Отправлено уведомление о проблемах с командами для рейда ${raid.date} ${raid.time} ${raid.raidType}`);
      }
    } catch (error) {
      console.error(`Ошибка при проверке команд для рейда ${raid.date} ${raid.time} ${raid.raidType}:`, error.message);
    }
  }

  /**
   * Очистка всех таймеров (для корректного завершения работы)
   */
  cleanup() {
    for (const [raidKey, timer] of this.mythicRaidTimers) {
      clearTimeout(timer);
      console.log(`Очищен таймер для рейда: ${raidKey}`);
    }
    this.mythicRaidTimers.clear();

    // Очищаем все динамические cron-задачи
    for (const [key, job] of this.dynamicCronJobs) {
      job.stop();
      job.destroy();
      console.log(`Очищена динамическая cron-задача: ${key}`);
    }
    this.dynamicCronJobs.clear();
  }

  /**
   * Ручная проверка всех команд (команда /teams)
   */
  async checkAllTeamsManual() {
    try {
      if (!this.initialized) {
        return '❌ <b>Сервис мониторинга не инициализирован</b>';
      }

      console.log('Выполняется ручная проверка всех команд...');

      // Получаем данные
      const [clients, schedule] = await Promise.all([
        this.sheetsService.getClientsData(),
        this.scheduleService.getCombinedScheduleData(),
      ]);

      const internalSchedule = await this.internalScheduleService.getInternalScheduleData();

      // Фильтруем рейды начиная с сегодняшнего дня (включая закрытые)
      const now = DateUtils.now();
      const relevantRaids = schedule.filter(raid => {
        const raidDateTime = DateUtils.createDateTime(raid.date, raid.time);
        if (!raidDateTime) return false;

        // Рейды начиная с сегодняшнего дня (включая закрытые)
        return DateUtils.isDateTodayOrFuture(raidDateTime);
      });

      if (relevantRaids.length === 0) {
        return '📅 <b>Нет открытых рейдов начиная с сегодняшнего дня</b>';
      }

      console.log(`Проверяем команды для ${relevantRaids.length} рейдов`);

      const allIssues = [];
      const raidStats = [];

      // 1. Проверяем соответствие типов рейдов (форсированная проверка)
      const typeMismatches = await this.checkRaidTypeMismatchesForced(clients, schedule);
      if (typeMismatches.length > 0) {
        allIssues.push({
          type: 'type_mismatches',
          mismatches: typeMismatches
        });
      }

      // 1.1. Форсированная проверка заказов на прошедшую дату (граница 05:00 CET)
      const pastDateOrders = this.checkPastDateOrdersForced(clients);
      if (pastDateOrders.length > 0) {
        allIssues.push({
          type: 'past_date_orders',
          items: pastDateOrders
        });
      }

      // 1.2. Форсированная проверка заказов 3+ часа назад
      const forcedPastTimeOrders = this.checkOldOrdersByTime(clients, 180)
        .map(it => ({ rowNumber: it.rowNumber, date: it.date, time: it.time }));
      if (forcedPastTimeOrders.length > 0) {
        allIssues.push({
          type: 'past_time_orders',
          items: forcedPastTimeOrders
        });
      }

      // 2. Проверяем каждый рейд на проблемы с командами
      for (const raid of relevantRaids) {
        // Получаем клиентов для этого рейда
        const raidClients = clients.filter(client =>
          client.date === raid.date &&
          client.time === raid.time &&
          RaidUtils.isRaidTypeAllowed(raid.raidType, client.raidType)
        );

        const raidStat = {
          raid: raid,
          clientCount: raidClients.length,
          emptyTeams: 0,
          wrongTeams: 0,
          issues: []
        };

        if (raidClients.length > 0) {
          // 1. Проверяем пустые команды
          const emptyTeamIssues = RaidUtils.checkTeamAssignments(raid, raidClients);
          raidStat.emptyTeams = emptyTeamIssues.length;

          if (emptyTeamIssues.length > 0) {
            raidStat.issues.push(...emptyTeamIssues.map(issue => `❌ ${issue}`));
            allIssues.push({
              type: 'empty_teams',
              raid: raid,
              issues: emptyTeamIssues
            });
          }

          // 2. Проверяем правильность команд
          const teamMismatches = await this.checkTeamMismatches(raidClients, internalSchedule, raid);
          raidStat.wrongTeams = teamMismatches.length;

          if (teamMismatches.length > 0) {
            teamMismatches.forEach(mismatch => {
              const orderInfo = mismatch.orderId ? `Заказ: "${mismatch.orderId}" ` : '';
              raidStat.issues.push(`❌ Строка ${mismatch.rowNumber}: ${orderInfo}команда "${mismatch.clientTeam}" не соответствует расписанию`);
            });
            allIssues.push({
              type: 'wrong_teams',
              raid: raid,
              mismatches: teamMismatches
            });
          }
        }

        raidStats.push(raidStat);
      }

      // Формируем отчет
      return await this.formatTeamsReport(raidStats, allIssues, internalSchedule);

    } catch (error) {
      console.error('Ошибка при ручной проверке команд:', error.message);
      return `❌ <b>Ошибка при проверке команд:</b> ${error.message}`;
    }
  }

  /**
   * Форматирование отчета о проверке команд
   */
  async formatTeamsReport(raidStats, allIssues, internalSchedule) {
    let report = '🛡️ <b>Отчет о проверке команд</b>\n\n';

    // Общая статистика
    const totalRaids = raidStats.length;
    const openRaids = raidStats.filter(stat => stat.raid.status !== 'закрыт').length;
    const closedRaids = raidStats.filter(stat => stat.raid.status === 'закрыт').length;
    const raidsWithIssues = raidStats.filter(stat => stat.issues.length > 0).length;
    const totalEmptyTeams = raidStats.reduce((sum, stat) => sum + stat.emptyTeams, 0);
    const totalWrongTeams = raidStats.reduce((sum, stat) => sum + stat.wrongTeams, 0);

    // Подсчитываем проблемы с типами рейдов
    const typeMismatchIssues = allIssues.filter(issue => issue.type === 'type_mismatches');
    const totalTypeMismatches = typeMismatchIssues.reduce((sum, issue) => sum + issue.mismatches.length, 0);

    report += `📊 <b>Общая статистика:</b>\n`;
    report += `• Проверено рейдов: ${totalRaids} (открытых: ${openRaids}, закрытых: ${closedRaids})\n`;
    report += `• Рейдов с проблемами: ${raidsWithIssues}\n`;
    report += `• Пустых команд: ${totalEmptyTeams}\n`;
    report += `• Неправильных команд: ${totalWrongTeams}\n`;
    if (totalTypeMismatches > 0) {
      report += `• Несоответствий типов рейдов: ${totalTypeMismatches}\n`;
    }
    report += '\n';

    if (allIssues.length === 0) {
      report += '✅ <b>Все команды заполнены правильно и типы рейдов соответствуют!</b>';
      return report;
    }

    // Отображаем проблемы с типами рейдов
    if (typeMismatchIssues.length > 0) {
      report += '⚠️ <b>Несоответствие типов рейдов:</b>\n\n';

      typeMismatchIssues.forEach(issue => {
        issue.mismatches.forEach(mismatch => {
          report += `• ${mismatch.date} ${mismatch.time}: расписание "${mismatch.scheduleRaidType}" ≠ клиенты "${mismatch.clientRaidType}" (${mismatch.clientCount} клиентов)\n`;
          if (Array.isArray(mismatch.clientsDetailed) && mismatch.clientsDetailed.length > 0) {
            const details = mismatch.clientsDetailed
              .sort((a, b) => (a.rowNumber || 0) - (b.rowNumber || 0))
              .map(cd => {
                const orderInfo = cd.orderId ? `Заказ: "${cd.orderId}" ` : '';
                const typeInfo = cd.clientRaidType ? `тип рейда "${cd.clientRaidType}"` : 'тип рейда клиента';
                return `  ❌ Строка ${cd.rowNumber}: ${orderInfo}${typeInfo} не соответствует расписанию`;
              })
              .join('\n');
            report += `${details}\n`;
          }
        });
      });

      report += '\n';
    }

    // Заказы на прошедшую дату
    const pastDateIssues = allIssues.filter(issue => issue.type === 'past_date_orders');
    if (pastDateIssues.length > 0) {
      report += '⚠️ <b>Заказы на прошедшую дату:</b>\n\n';
      pastDateIssues.forEach(issue => {
        issue.items.forEach(it => {
          report += `• Строка ${it.rowNumber}, дата ${it.date} - проверьте, появился заказ на уже прошедшую дату\n`;
        });
      });
      report += '\n';
    }

    // Заказы со временем 3+ часа назад
    const pastTimeIssues = allIssues.filter(issue => issue.type === 'past_time_orders');
    if (pastTimeIssues.length > 0) {
      report += '⏰ <b>Заказы со временем 3+ часа назад:</b>\n\n';
      pastTimeIssues.forEach(issue => {
        issue.items.forEach(it => {
          report += `• Строка ${it.rowNumber}, ${it.date} ${it.time}\n`;
        });
      });
      report += '\n';
    }

    // Детальный отчет по рейдам с проблемами
    report += '⚠️ <b>Рейды с проблемами:</b>\n\n';

    const raidsWithProblems = raidStats.filter(stat => stat.issues.length > 0);

    // Группируем рейды по дате, времени и команде
    const groupedRaids = this.groupRaidsByDateTime(raidsWithProblems, internalSchedule);

    let groupIndex = 1;
    for (const group of groupedRaids) {
      // Получаем ожидаемые команды для этой группы
      const expectedTeams = this.getExpectedTeamsForGroup(group, internalSchedule);

      // Заголовок группы
      if (expectedTeams.length > 0) {
        report += `<b>Ожидаемая команда:</b> ${expectedTeams.join(', ')}\n`;
      }

      // Названия рейдов в группе
      const raidNames = group.raids.map(r => r.raidType).join(' / ');
      report += `${groupIndex}. <b>${raidNames}</b>\n`;
      report += `📅 ${group.date} ${group.time}\n`;
      report += `🔒 Статус: ${group.status}\n`;
      report += `👥 Клиентов: ${group.totalClients}\n`;
      report += `❌ Проблем: ${group.totalIssues}\n\n`;

      // Используем уникальные проблемы из группы
      group.uniqueIssues.forEach(issue => {
        report += `   ${issue}\n`;
      });

      report += '\n';
      groupIndex++;
    }

    // Краткие рекомендации
    report += '💡 <b>Рекомендации:</b>\n';
    if (totalEmptyTeams > 0) {
      report += `• Заполните команды для ${totalEmptyTeams} клиентов\n`;
    }
    if (totalWrongTeams > 0) {
      report += `• Исправьте команды для ${totalWrongTeams} клиентов\n`;
    }
    if (totalTypeMismatches > 0) {
      report += `• Исправьте типы рейдов для ${totalTypeMismatches} групп клиентов\n`;
    }

    return report;
  }

  /**
   * Группировка рейдов по дате, времени и команде
   */
  groupRaidsByDateTime(raidsWithProblems, internalSchedule) {
    const groups = new Map();

    raidsWithProblems.forEach(raidStat => {
      const { raid } = raidStat;

      // Получаем команды для этого рейда из внутреннего расписания
      const expectedTeams = internalSchedule.filter(scheduleRaid =>
        scheduleRaid.date === raid.date &&
        scheduleRaid.time === raid.time &&
        RaidUtils.isRaidTypeAllowed(scheduleRaid.raidType, raid.raidType)
      ).map(scheduleRaid => scheduleRaid.team);

      // Ключ группировки: дата + время + команды
      // Группируем только рейды с одинаковыми командами
      const groupKey = `${raid.date}|${raid.time}|${expectedTeams.sort().join(',')}`;

      if (!groups.has(groupKey)) {
        groups.set(groupKey, {
          date: raid.date,
          time: raid.time,
          status: raid.status,
          expectedTeams: expectedTeams,
          raids: [],
          allClients: new Set(), // Используем Set для уникальных клиентов
          allIssues: new Set()   // Используем Set для уникальных проблем
        });
      }

      const group = groups.get(groupKey);
      group.raids.push(raidStat);

      // Добавляем уникальных клиентов и проблемы
      raidStat.issues.forEach(issue => {
        group.allIssues.add(issue);
      });
    });

    // Преобразуем Sets в числа для отображения
    return Array.from(groups.values()).map(group => ({
      ...group,
      totalClients: group.raids.reduce((sum, raid) => sum + raid.clientCount, 0),
      totalIssues: group.allIssues.size,
      uniqueIssues: Array.from(group.allIssues)
    }));
  }

  /**
   * Получение ожидаемых команд для группы рейдов
   */
  getExpectedTeamsForGroup(group, internalSchedule) {
    const expectedTeams = new Set();

    group.raids.forEach(raidStat => {
      const { raid } = raidStat;

      // Получаем команды для этого рейда из внутреннего расписания
      const teams = internalSchedule.filter(scheduleRaid =>
        scheduleRaid.date === raid.date &&
        scheduleRaid.time === raid.time &&
        RaidUtils.isRaidTypeAllowed(scheduleRaid.raidType, raid.raidType)
      ).map(scheduleRaid => scheduleRaid.team);

      teams.forEach(team => expectedTeams.add(team));
    });

    return Array.from(expectedTeams);
  }

  /**
   * Получение статистики мониторинга
   */
  getStats() {
    return {
      activeTimers: this.mythicRaidTimers.size,
      dynamicCronJobs: this.dynamicCronJobs.size,
      previousClientsCount: this.previousClients.length,
      initialized: this.initialized,
    };
  }

  /**
   * Проверка аккаунтов для предстоящих рейдов за 30 минут
   */
  async checkAccountsForUpcomingRaids() {
    if (!this.initialized) {
      console.error('RaidMonitor не инициализирован');
      return;
    }

    try {
      console.log('Проверяем аккаунты для предстоящих рейдов...');

      // Получаем текущее время в CET/CEST
      const now = new Date();
      const cetTime = new Date(now.toLocaleString('en-US', { timeZone: config.timezone }));
      
      // Вычисляем время через 30 минут
      const targetTime = new Date(cetTime.getTime() + config.monitoring.accountsReminderMinutes * 60 * 1000);
      
      // Форматируем время в формат HH:MM для сравнения с данными из таблицы
      const targetTimeString = targetTime.toLocaleTimeString('en-US', { 
        timeZone: config.timezone,
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });

      console.log(`Проверяем рейды на время: ${targetTimeString}`);

      // Получаем данные об аккаунтах для этого времени
      const raidsWithAccounts = await this.sheetsService.getAccountsForUpcomingRaids(targetTimeString);

      if (raidsWithAccounts.length === 0) {
        console.log('Нет рейдов с аккаунтами для указанного времени');
        return;
      }

      // Подсчитываем общее количество аккаунтов
      const totalAccounts = raidsWithAccounts.reduce((sum, raid) => sum + raid.totalAccounts, 0);

      console.log(`Найдено ${raidsWithAccounts.length} рейдов с ${totalAccounts} аккаунтами на время ${targetTimeString}`);

      // Проверяем, не отправляли ли мы уже уведомление для этого времени
      const notificationKey = `accounts_${targetTimeString}`;
      
      if (this.notifiedStore.isNotified(notificationKey)) {
        console.log(`Уведомление об аккаунтах для времени ${targetTimeString} уже отправлено`);
        return;
      }

      // Отправляем уведомление
      const message = config.messages.accountsReminder(targetTimeString, totalAccounts);
      
      await this.notificationService.sendNotification(message);
      
      // Отмечаем, что уведомление отправлено
      this.notifiedStore.markAsNotified(notificationKey);
      
      console.log(`Отправлено уведомление об аккаунтах: ${totalAccounts} аккаунтов на время ${targetTimeString}`);

    } catch (error) {
      console.error('Ошибка при проверке аккаунтов для предстоящих рейдов:', error.message);
    }
  }
}

module.exports = RaidMonitor;