# ✅ Чеклист деплоя на VPS

## 📋 Подготовка

- [ ] VPS с Ubuntu 20.04+ готов
- [ ] SSH доступ к серверу настроен
- [ ] Создан лист `config` в Google Sheets таблице
- [ ] Выполнена команда `npm run init-config` локально

## 🔧 Установка на сервере

- [ ] Установлен Node.js 18+: `curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && apt-get install -y nodejs`
- [ ] Установлен PM2: `npm install -g pm2`
- [ ] Клонирован репозиторий: `git clone https://github.com/your-username/reminderbot.git`
- [ ] Установлены зависимости: `npm install`

## 🔧 Настройка переменных

- [ ] Создан файл `.env`: `cp .env.example .env`
- [ ] Заполнены все переменные в `.env`
- [ ] Проверены переменные: `npm run check-env`

### Обязательные переменные:

```bash
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID=1IPd9yEJzx_PTOVFXPmNpgL6zYINVKwBKTFvReMmTKNY
GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID=1dzPdrJGulpXPlTEhsoiXtGQCwGQJo8d60Wlv4igBQOM
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
ADMIN_PASSWORD=your_secure_password
MODE=both
USE_GOOGLE_SHEETS_CONFIG=true
TIMEZONE=Europe/Berlin
```

## 🚀 Запуск

- [ ] Инициализация Google Sheets: `npm run init-config`
- [ ] Запуск с PM2: `pm2 start index.js --name "wow-raid-bot"`
- [ ] Настройка автозапуска: `pm2 startup && pm2 save`
- [ ] Проверка статуса: `pm2 status`

## ✅ Проверка работы

- [ ] Процесс запущен: `pm2 status`
- [ ] Логи без ошибок: `pm2 logs wow-raid-bot`
- [ ] Веб-интерфейс доступен: `http://your-server-ip:3000/admin`
- [ ] Telegram команды работают: `/help`, `/test`, `/filled`

## 🔧 Дополнительно

- [ ] Настроен Nginx (опционально)
- [ ] Настроен SSL сертификат (опционально)
- [ ] Настроен firewall: `ufw enable`
- [ ] Настроены автоматические бэкапы

## 🛠️ Полезные команды после деплоя

```bash
# Статус процессов
pm2 status

# Логи в реальном времени
pm2 logs wow-raid-bot --lines 50

# Перезапуск
pm2 restart wow-raid-bot

# Обновление кода
cd /path/to/reminderbot
git pull
npm install
pm2 restart wow-raid-bot

# Мониторинг
pm2 monit
```

---

**🎯 Результат**: Полнофункциональный WoW Raid Bot работает на VPS 24/7!
