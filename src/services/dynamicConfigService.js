const ConfigSheetsService = require('./configSheetsService');

/**
 * Сервис для динамической загрузки конфигурации из Google Sheets
 */
class DynamicConfigService {
  constructor() {
    this.configSheetsService = new ConfigSheetsService();
    this.cachedConfig = null;
    this.lastLoadTime = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5 минут
    this.initialized = false;
  }

  /**
   * Инициализация сервиса
   */
  async initialize() {
    try {
      await this.configSheetsService.initialize();
      this.initialized = true;
      console.log('DynamicConfigService успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации DynamicConfigService:', error.message);
      throw error;
    }
  }

  /**
   * Загрузка конфигурации из Google Sheets с кэшированием
   */
  async loadConfig() {
    if (!this.initialized) {
      console.warn('DynamicConfigService не инициализирован, возвращаем null');
      return null;
    }

    // Проверяем, нужно ли обновить кэш
    const now = Date.now();
    if (this.cachedConfig && this.lastLoadTime && (now - this.lastLoadTime) < this.cacheTimeout) {
      console.log('Возвращаем кэшированную конфигурацию');
      return this.cachedConfig;
    }

    try {
      console.log('Загрузка конфигурации из Google Sheets...');
      const config = await this.configSheetsService.loadConfig();
      
      this.cachedConfig = config;
      this.lastLoadTime = now;
      
      console.log('Конфигурация успешно загружена из Google Sheets');
      return config;
    } catch (error) {
      console.error('Ошибка загрузки конфигурации из Google Sheets:', error.message);
      
      // Если есть кэшированная конфигурация, возвращаем её
      if (this.cachedConfig) {
        console.log('Возвращаем кэшированную конфигурацию из-за ошибки загрузки');
        return this.cachedConfig;
      }
      
      return null;
    }
  }

  /**
   * Принудительное обновление кэша
   */
  async refreshConfig() {
    this.cachedConfig = null;
    this.lastLoadTime = null;
    return await this.loadConfig();
  }

  /**
   * Получение конкретной секции конфигурации
   */
  async getConfigSection(sectionName) {
    const config = await this.loadConfig();
    return config ? config[sectionName] : null;
  }

  /**
   * Получение allowedRaidTypes
   */
  async getAllowedRaidTypes() {
    return await this.getConfigSection('allowedRaidTypes');
  }

  /**
   * Получение настроек мониторинга
   */
  async getMonitoringConfig() {
    return await this.getConfigSection('monitoring');
  }

  /**
   * Получение лимитов команд
   */
  async getTeamCapacityLimits() {
    return await this.getConfigSection('teamCapacityLimits');
  }

  /**
   * Получение типов "ласт босс"
   */
  async getLastBossRaidTypes() {
    return await this.getConfigSection('lastBossRaidTypes');
  }
}

module.exports = DynamicConfigService;
