const { google } = require('googleapis');
const { config } = require('../config/config');

/**
 * Сервис для работы с конфигурацией в Google Sheets
 */
class ConfigSheetsService {
  constructor() {
    this.sheets = null;
    this.auth = null;
    this.initialized = false;
    this.configSpreadsheetId = '1ejznOYfsoPsSAyjy5esgfmLpzyyM9b4CGE1b3oO-BKY';
    this.configSheetName = 'config';
    this.configRange = 'A:C'; // Ключ, Значение, Описание
  }

  /**
   * Инициализация Google Sheets API
   */
  async initialize() {
    try {
      if (!config.googleSheets.serviceAccountKey) {
        throw new Error('Не указан ключ сервисного аккаунта Google');
      }

      // Парсим JSON ключ сервисного аккаунта
      let serviceAccountKey;
      if (typeof config.googleSheets.serviceAccountKey === 'string') {
        try {
          serviceAccountKey = JSON.parse(config.googleSheets.serviceAccountKey);
        } catch (error) {
          // Пробуем очистить строку от лишних символов
          let cleanedKey = config.googleSheets.serviceAccountKey;

          // Убираем внешние кавычки если есть
          if (cleanedKey.startsWith('"') && cleanedKey.endsWith('"')) {
            cleanedKey = cleanedKey.slice(1, -1);
          }

          // Убираем экранирование
          cleanedKey = cleanedKey.replace(/\\"/g, '"').replace(/\\\\/g, '\\');

          serviceAccountKey = JSON.parse(cleanedKey);
        }
      } else {
        serviceAccountKey = config.googleSheets.serviceAccountKey;
      }

      // Создаем JWT аутентификацию
      this.auth = new google.auth.JWT(
        serviceAccountKey.client_email,
        null,
        serviceAccountKey.private_key,
        ['https://www.googleapis.com/auth/spreadsheets']
      );

      // Инициализируем Sheets API
      this.sheets = google.sheets({ version: 'v4', auth: this.auth });
      this.initialized = true;

      console.log('ConfigSheetsService успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации ConfigSheetsService:', error.message);
      throw error;
    }
  }

  /**
   * Проверка инициализации
   */
  _checkInitialized() {
    if (!this.initialized) {
      throw new Error('ConfigSheetsService не инициализирован. Вызовите initialize() сначала.');
    }
  }

  /**
   * Загрузка конфигурации из Google Sheets
   */
  async loadConfig() {
    this._checkInitialized();

    try {
      console.log('Загрузка конфигурации из Google Sheets...');

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.configSpreadsheetId,
        range: `${this.configSheetName}!${this.configRange}`,
      });

      const rows = response.data.values || [];
      console.log(`Получено ${rows.length} строк конфигурации`);

      // Преобразуем строки в объект конфигурации
      const configData = this.parseConfigRows(rows);
      return configData;
    } catch (error) {
      console.error('Ошибка загрузки конфигурации:', error.message);
      throw error;
    }
  }

  /**
   * Сохранение конфигурации в Google Sheets
   */
  async saveConfig(configData) {
    this._checkInitialized();

    try {
      console.log('Сохранение конфигурации в Google Sheets...');

      // Преобразуем объект конфигурации в строки
      const rows = this.configToRows(configData);

      // Очищаем существующие данные
      await this.sheets.spreadsheets.values.clear({
        spreadsheetId: this.configSpreadsheetId,
        range: `${this.configSheetName}!${this.configRange}`,
      });

      // Записываем новые данные
      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.configSpreadsheetId,
        range: `${this.configSheetName}!A1`,
        valueInputOption: 'RAW',
        resource: {
          values: rows
        }
      });

      console.log('Конфигурация успешно сохранена в Google Sheets');
    } catch (error) {
      console.error('Ошибка сохранения конфигурации:', error.message);
      throw error;
    }
  }

  /**
   * Преобразование строк из таблицы в объект конфигурации
   */
  parseConfigRows(rows) {
    const config = {
      monitoring: {},
      allowedRaidTypes: {},
      teamCapacityLimits: {},
      lastBossRaidTypes: []
    };

    rows.forEach(row => {
      if (row.length < 2) return; // Пропускаем пустые строки

      const key = row[0];
      const value = row[1];
      const description = row[2] || '';

      // Парсим ключи и устанавливаем значения
      this.setNestedValue(config, key, this.parseValue(value));
    });

    return config;
  }

  /**
   * Преобразование объекта конфигурации в строки для таблицы
   */
  configToRows(configData) {
    const rows = [
      ['Ключ', 'Значение', 'Описание'] // Заголовок
    ];

    // Настройки мониторинга
    if (configData.monitoring) {
      rows.push(['monitoring.checkIntervalMinutes', configData.monitoring.checkIntervalMinutes, 'Интервал проверки (минуты)']);
      rows.push(['monitoring.mythicNotificationDelayMinutes', configData.monitoring.mythicNotificationDelayMinutes, 'Задержка уведомлений о мификах (минуты)']);
      rows.push(['monitoring.preRaidCheckMinutes', configData.monitoring.preRaidCheckMinutes, 'Проверка команд до рейда (минуты)']);
      rows.push(['monitoring.minClientsThreshold', configData.monitoring.minClientsThreshold, 'Минимальный порог клиентов']);
    }

    // Допустимые типы рейдов
    if (configData.allowedRaidTypes) {
      Object.entries(configData.allowedRaidTypes).forEach(([scheduleType, allowedTypes]) => {
        rows.push([`allowedRaidTypes.${scheduleType}`, JSON.stringify(allowedTypes), `Допустимые типы для ${scheduleType}`]);
      });
    }

    // Лимиты команд
    if (configData.teamCapacityLimits) {
      Object.entries(configData.teamCapacityLimits).forEach(([teamName, limits]) => {
        rows.push([`teamCapacityLimits.${teamName}.normal`, limits.normal, `Лимит Normal для команды ${teamName}`]);
        rows.push([`teamCapacityLimits.${teamName}.heroic`, limits.heroic, `Лимит Heroic для команды ${teamName}`]);
      });
    }

    // Типы "ласт босс"
    if (configData.lastBossRaidTypes) {
      rows.push(['lastBossRaidTypes', JSON.stringify(configData.lastBossRaidTypes), 'Типы рейдов "ласт босс"']);
    }

    return rows;
  }

  /**
   * Установка вложенного значения по ключу
   */
  setNestedValue(obj, key, value) {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current)) {
        current[k] = {};
      }
      current = current[k];
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * Парсинг значения из строки
   */
  parseValue(value) {
    if (value === '') return '';
    
    // Пробуем парсить как число
    if (!isNaN(value) && !isNaN(parseFloat(value))) {
      return parseFloat(value);
    }

    // Пробуем парсить как JSON
    try {
      return JSON.parse(value);
    } catch {
      // Возвращаем как строку
      return value;
    }
  }

  /**
   * Инициализация таблицы конфигурации с значениями по умолчанию
   */
  async initializeConfigSheet() {
    try {
      const defaultConfig = {
        monitoring: {
          checkIntervalMinutes: 10,
          mythicNotificationDelayMinutes: 15,
          preRaidCheckMinutes: 10,
          minClientsThreshold: 20
        },
        allowedRaidTypes: {
          'LoU Heroic': ['LoU Heroic', 'LoU Heroic Unsaved', 'Gallywix Heroic', 'LoU Single Boss HC', 'Prototype A.S.M.R.'],
          'LoU Heroic Unsaved': ['LoU Heroic', 'LoU Heroic Unsaved', 'Gallywix Heroic', 'LoU Single Boss HC', 'Prototype A.S.M.R.'],
          'LoU Mythic': ['LoU Mythic', 'LoU Single Boss Mythic', 'LoU Mythic 8/8 FP 20ppl'],
          'LoU Normal': ['LoU Normal', 'LoU Normal Unsaved', 'Gallywix Normal', 'LoU Single Boss NM'],
          'LoU Normal Unsaved': ['LoU Normal', 'LoU Normal Unsaved', 'Gallywix Normal', 'LoU Single Boss NM'],
          'MO Normal': ['MO Normal', 'MO Normal Unsaved', 'Dimensius Normal', 'MO Single Boss NM'],
          'MO Normal Unsaved': ['MO Normal', 'MO Normal Unsaved', 'Dimensius Normal', 'MO Single Boss NM']
        },
        teamCapacityLimits: {
          'h2s': { normal: 20, heroic: 17 },
          'a2s': { normal: 21, heroic: 13 },
          'a1': { normal: 21, heroic: 11 },
          'h1': { normal: 20, heroic: 12 }
        },
        lastBossRaidTypes: ['Gallywix Heroic', 'Gallywix Normal', 'Gallywix Mythic']
      };

      await this.saveConfig(defaultConfig);
      console.log('Таблица конфигурации инициализирована значениями по умолчанию');
    } catch (error) {
      console.error('Ошибка инициализации таблицы конфигурации:', error.message);
      throw error;
    }
  }
}

module.exports = ConfigSheetsService;
