const { google } = require('googleapis');
const { config } = require('../config/config');
const DateUtils = require('../utils/dateUtils');
const InternalScheduleService = require('./internalScheduleService');

/**
 * Сервис для работы с Google Sheets API (расписание рейдов)
 */
class ScheduleSheetsService {
  constructor() {
    this.sheets = null;
    this.initialized = false;
    this.internalScheduleService = new InternalScheduleService();
  }

  /**
   * Инициализация Google Sheets API
   */
  async initialize() {
    try {
      console.log('Инициализация ScheduleSheetsService...');

      // Парсим ключ сервисного аккаунта
      const serviceAccountKey = JSON.parse(config.googleSheets.serviceAccountKey);

      // Создаем JWT клиент
      const auth = new google.auth.JWT(
        serviceAccountKey.client_email,
        null,
        serviceAccountKey.private_key,
        ['https://www.googleapis.com/auth/spreadsheets.readonly']
      );

      // Инициализируем Sheets API
      this.sheets = google.sheets({ version: 'v4', auth });

      // Инициализируем внутреннее расписание
      await this.internalScheduleService.initialize();

      this.initialized = true;
      console.log('ScheduleSheetsService успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации ScheduleSheetsService:', error.message);
      throw error;
    }
  }

  /**
   * Получение данных расписания из Google Sheets
   */
  async getScheduleData() {
    if (!this.initialized) {
      throw new Error('ScheduleSheetsService не инициализирован');
    }

    try {
      console.log('Получение данных расписания из Google Sheets...');

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: config.googleSheets.scheduleSpreadsheetId,
        range: `${config.googleSheets.scheduleSheetName}!${config.googleSheets.scheduleRange}`,
      });

      const rows = response.data.values || [];
      console.log(`Получено ${rows.length} строк из таблицы расписания`);

      return this.parseScheduleData(rows);
    } catch (error) {
      console.error('Ошибка получения данных расписания:', error.message);
      throw error;
    }
  }

  /**
   * Парсинг данных расписания из строк таблицы
   */
  parseScheduleData(rows) {
    const schedule = [];
    let currentDate = null;
    let currentDayName = null;

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];

      // Пропускаем пустые строки
      if (!row || row.length === 0) {
        continue;
      }

      // Проверяем, является ли строка заголовком дня недели
      if (this.isDayHeader(row, i)) {
        currentDayName = row[0];

        // Дата находится в столбце D этой же строки
        if (row[3]) {
          currentDate = this.parseDate(row[3]);
        } else {
          // Попробуем найти дату в следующей строке
          if (i + 1 < rows.length && rows[i + 1] && rows[i + 1][3]) {
            currentDate = this.parseDate(rows[i + 1][3]);
          }
        }
        continue;
      }

      // Если у нас есть текущая дата и это строка с рейдом
      if (currentDate && row[0] && row[1]) {
        const time = this.parseTime(row[0]);
        const raidType = row[1].trim();
        const isClosed = this.parseStatus(row[3]);

        if (time && raidType) {
          const entry = {
            date: this.formatDateForDisplay(currentDate),
            time: time,
            raidType: raidType,
            status: isClosed ? 'закрыт' : 'открыт',
            dayName: currentDayName
          };

          schedule.push(entry);
        }
      }
    }

    console.log(`Обработано ${schedule.length} записей расписания`);
    return schedule;
  }

  /**
   * Проверка, является ли строка заголовком дня недели
   */
  isDayHeader(row, rowIndex) {
    const dayNames = ['понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота', 'воскресенье'];
    const cellValue = row[0] ? row[0].toLowerCase().trim() : '';

    return dayNames.some(day => cellValue.includes(day));
  }

  /**
   * Парсинг даты из строки или объекта Date
   */
  parseDate(dateValue) {
    if (!dateValue) return null;

    try {
      // Если это уже объект Date
      if (dateValue instanceof Date) {
        return dateValue;
      }

      // Если это строка, пытаемся распарсить
      if (typeof dateValue === 'string') {
        // Сначала пробуем европейский формат DD.MM.YYYY
        const europeanMatch = dateValue.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
        if (europeanMatch) {
          const [, day, month, year] = europeanMatch;
          const date = new Date(year, month - 1, day); // month - 1 потому что месяцы в JS начинаются с 0

          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Если не получилось, пробуем стандартный парсинг
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Если это число (timestamp)
      if (typeof dateValue === 'number') {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      return null;
    } catch (error) {
      console.error('Ошибка парсинга даты:', dateValue, error.message);
      return null;
    }
  }

  /**
   * Парсинг времени из строки
   */
  parseTime(timeValue) {
    if (!timeValue) return null;

    try {
      const timeStr = timeValue.toString().trim();

      // Ищем паттерн HH:MM
      const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})/);
      if (timeMatch) {
        const hours = timeMatch[1].padStart(2, '0');
        const minutes = timeMatch[2];
        return `${hours}:${minutes}`;
      }

      return null;
    } catch (error) {
      console.error('Ошибка парсинга времени:', timeValue, error.message);
      return null;
    }
  }

  /**
   * Парсинг статуса (чекбокс)
   */
  parseStatus(statusValue) {
    if (!statusValue) return false;

    // Чекбокс может быть TRUE, "TRUE", true, или другими вариантами
    const statusStr = statusValue.toString().toLowerCase().trim();
    return statusStr === 'true' || statusStr === '1' || statusValue === true;
  }

  /**
   * Форматирование даты для отображения (DD.MM.YYYY)
   */
  formatDateForDisplay(date) {
    if (!date) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
  }

  /**
   * Получение открытых рейдов
   */
  async getOpenRaids() {
    const schedule = await this.getCombinedScheduleData();
    return schedule.filter(item => item.status !== 'закрыт');
  }

  /**
   * Получение открытых рейдов из объединенного расписания
   */
  async getOpenRaidsFromCombined() {
    const schedule = await this.getCombinedScheduleData();
    return schedule.filter(item => item.status !== 'закрыт');
  }

  /**
   * Получение всех рейдов из объединенного расписания (включая закрытые)
   */
  async getAllRaidsFromCombined() {
    return await this.getCombinedScheduleData();
  }

  /**
   * Получение объединенного расписания (внешнее + внутреннее)
   */
  async getCombinedScheduleData() {
    if (!this.initialized) {
      throw new Error('ScheduleSheetsService не инициализирован');
    }

    try {
      console.log('Получение объединенного расписания...');

      // Получаем данные из внешнего и внутреннего расписания
      const [externalSchedule, internalSchedule] = await Promise.all([
        this.getScheduleData(),
        this.internalScheduleService.getInternalScheduleData()
      ]);

      console.log(`Внешнее расписание: ${externalSchedule.length} записей`);
      console.log(`Внутреннее расписание: ${internalSchedule.length} записей`);

      // Создаем Map для быстрого поиска рейдов по ключу (дата+время+тип)
      const externalRaidMap = new Map();
      externalSchedule.forEach(raid => {
        const key = `${raid.date}|${raid.time}|${raid.raidType}`;
        externalRaidMap.set(key, raid);
      });

      // Создаем Map для внутреннего расписания
      const internalRaidMap = new Map();
      internalSchedule.forEach(raid => {
        const key = `${raid.date}|${raid.time}|${raid.raidType}`;
        if (!internalRaidMap.has(key)) {
          internalRaidMap.set(key, []);
        }
        internalRaidMap.get(key).push(raid);
      });

      // Объединяем расписания
      const combinedSchedule = [...externalSchedule];

      // Добавляем уникальные рейды из внутреннего расписания как "закрытые"
      for (const [key, internalRaids] of internalRaidMap) {
        if (!externalRaidMap.has(key)) {
          // Это уникальный рейд из внутреннего расписания
          const firstRaid = internalRaids[0];
          
          // Создаем запись для внешнего расписания
          const newRaid = {
            date: firstRaid.date,
            time: firstRaid.time,
            raidType: firstRaid.raidType,
            status: 'закрыт', // По умолчанию закрыт
            dayName: firstRaid.dayName,
            source: 'internal', // Помечаем источник
            teams: internalRaids.map(r => r.team) // Сохраняем информацию о командах
          };

          combinedSchedule.push(newRaid);
          console.log(`Добавлен уникальный рейд из внутреннего расписания: ${newRaid.date} ${newRaid.time} ${newRaid.raidType}`);
        }
      }

      console.log(`Объединенное расписание: ${combinedSchedule.length} записей`);
      return combinedSchedule;
    } catch (error) {
      console.error('Ошибка получения объединенного расписания:', error.message);
      throw error;
    }
  }
}

module.exports = ScheduleSheetsService;