<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WoW Raid Bot - Админ-панель</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .section {
            background: white;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.2rem;
        }

        .section-content {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 3px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            min-height: 200px;
        }

        .actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .dynamic-field {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
        }

        .dynamic-field-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .dynamic-field-title {
            font-weight: 600;
            color: #495057;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .allowed-types-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .allowed-type-tag {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .allowed-type-tag .remove-tag {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 1rem;
            line-height: 1;
        }

        .allowed-type-tag .remove-tag:hover {
            color: #dc3545;
        }

        .add-allowed-type {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .add-allowed-type input {
            flex: 1;
        }

        .add-allowed-type button {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }

        .add-allowed-type button:hover {
            background: #218838;
        }

        .capacity-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .header {
                padding: 1rem;
            }

            .actions {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .capacity-inputs {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 WoW Raid Bot - Админ-панель</h1>
        <button class="logout-btn" onclick="logout()">Выйти</button>
    </div>

    <div class="container">
        <div id="alerts"></div>
        
        <div class="loading" id="loading"></div>
        
        <div id="configForm" style="display: none;">
            <!-- Настройки мониторинга -->
            <div class="section">
                <div class="section-header">
                    <h2>⚙️ Настройки мониторинга</h2>
                </div>
                <div class="section-content">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="checkInterval">Интервал проверки (минуты):</label>
                            <input type="number" id="checkInterval" min="1" max="60">
                        </div>
                        <div class="form-group">
                            <label for="mythicDelay">Задержка уведомлений о мификах (минуты):</label>
                            <input type="number" id="mythicDelay" min="1" max="60">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="preRaidCheck">Проверка команд до рейда (минуты):</label>
                            <input type="number" id="preRaidCheck" min="1" max="60">
                        </div>
                        <div class="form-group">
                            <label for="minClients">Минимальный порог клиентов:</label>
                            <input type="number" id="minClients" min="1" max="50">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Допустимые типы рейдов -->
            <div class="section">
                <div class="section-header">
                    <h2>🎯 Допустимые типы рейдов</h2>
                </div>
                <div class="section-content">
                    <div id="allowedRaidTypesContainer">
                        <!-- Динамически генерируемые поля -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addRaidTypeMapping()">+ Добавить тип рейда</button>
                </div>
            </div>

            <!-- Лимиты команд -->
            <div class="section">
                <div class="section-header">
                    <h2>👥 Лимиты заполненности команд</h2>
                </div>
                <div class="section-content">
                    <div id="teamCapacityContainer">
                        <!-- Динамически генерируемые поля -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addTeamCapacity()">+ Добавить команду</button>
                </div>
            </div>

            <!-- Типы рейдов "ласт босс" -->
            <div class="section">
                <div class="section-header">
                    <h2>🏆 Типы рейдов "Ласт босс"</h2>
                </div>
                <div class="section-content">
                    <div id="lastBossContainer">
                        <!-- Динамически генерируемые поля -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addLastBossType()">+ Добавить тип</button>
                </div>
            </div>

            <div class="actions">
                <button type="button" class="btn btn-secondary" onclick="loadConfig()">Сбросить изменения</button>
                <button type="button" class="btn btn-primary" onclick="saveConfig()">Сохранить конфигурацию</button>
            </div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
