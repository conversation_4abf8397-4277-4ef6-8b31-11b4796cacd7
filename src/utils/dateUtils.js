const moment = require('moment-timezone');
const { config } = require('../config/config');

/**
 * Утилиты для работы с датами и временем
 */
class DateUtils {
  /**
   * Получить текущее время в настроенной временной зоне
   */
  static now() {
    return moment().tz(config.timezone);
  }

  /**
   * Парсинг даты из строки
   */
  static parseDate(dateString) {
    // Поддерживаем различные форматы дат
    const formats = [
      'DD.MM.YYYY',
      'DD/MM/YYYY',
      'YYYY-MM-DD',
      'DD-MM-YYYY',
      'D.M.YYYY',
      'D/M/YYYY',
    ];

    for (const format of formats) {
      const parsed = moment.tz(dateString, format, config.timezone);
      if (parsed.isValid()) {
        return parsed;
      }
    }

    return null;
  }

  /**
   * Парсинг времени из строки
   */
  static parseTime(timeString) {
    // Поддерживаем различные форматы времени
    const formats = [
      'HH:mm',
      'H:mm',
      'HH.mm',
      'H.mm',
      'HHmm',
    ];

    for (const format of formats) {
      const parsed = moment(timeString, format);
      if (parsed.isValid()) {
        return {
          hours: parsed.hours(),
          minutes: parsed.minutes(),
        };
      }
    }

    return null;
  }

  /**
   * Создание полного момента времени из даты и времени
   */
  static createDateTime(dateString, timeString) {
    const date = this.parseDate(dateString);
    const time = this.parseTime(timeString);

    if (!date || !time) {
      return null;
    }

    return date.clone()
      .hours(time.hours)
      .minutes(time.minutes)
      .seconds(0)
      .milliseconds(0);
  }

  /**
   * Проверка, находится ли время в пределах указанного интервала от текущего времени
   */
  static isWithinMinutes(dateTime, minutes) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const now = this.now();
    const diff = dateTime.diff(now, 'minutes');

    return diff >= 0 && diff <= minutes;
  }

  /**
   * Проверка, прошло ли указанное количество минут с момента времени
   */
  static hasPassedMinutes(dateTime, minutes) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const now = this.now();
    const diff = now.diff(dateTime, 'minutes');

    return diff >= minutes;
  }

  /**
   * Форматирование даты для отображения
   */
  static formatDate(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return '';
    }

    return dateTime.format('DD.MM.YYYY');
  }

  /**
   * Форматирование времени для отображения
   */
  static formatTime(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return '';
    }

    return dateTime.format('HH:mm');
  }

  /**
   * Форматирование полной даты и времени для отображения
   */
  static formatDateTime(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return '';
    }

    return dateTime.format('DD.MM.YYYY HH:mm');
  }

  /**
   * Получение времени до события в минутах
   */
  static getMinutesUntil(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return null;
    }

    const now = this.now();
    return dateTime.diff(now, 'minutes');
  }

  /**
   * Проверка, является ли дата сегодняшней
   */
  static isToday(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const now = this.now();
    return dateTime.isSame(now, 'day');
  }

  /**
   * Проверка, является ли дата завтрашней
   */
  static isTomorrow(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const tomorrow = this.now().add(1, 'day');
    return dateTime.isSame(tomorrow, 'day');
  }

  /**
   * Проверка, является ли дата в прошлом (только дата, время не учитывается)
   */
  static isDateInPast(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const now = this.now();
    return dateTime.isBefore(now, 'day');
  }

  /**
   * Проверка, является ли дата сегодня или в будущем
   */
  static isDateTodayOrFuture(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }

    const now = this.now();
    return dateTime.isSameOrAfter(now, 'day');
  }

  /**
   * Текущий "рабочий" день с границей в 05:00 CET/CEST
   * Если сейчас до 05:00, считаем, что ещё предыдущий день
   */
  static getBusinessDayMoment() {
    const now = this.now();
    const business = now.clone();
    if (now.hours() < 5) {
      business.subtract(1, 'day');
    }
    return business.startOf('day');
  }

  /**
   * Строка даты текущего рабочего дня в формате DD.MM.YYYY
   */
  static getBusinessDayString() {
    return this.formatDate(this.getBusinessDayMoment());
  }

  /**
   * Проверка, что переданная дата (момент) строго раньше текущего рабочего дня (с учётом границы 05:00)
   */
  static isBeforeBusinessDay(dateTime) {
    if (!dateTime || !dateTime.isValid()) {
      return false;
    }
    const business = this.getBusinessDayMoment();
    return dateTime.isBefore(business, 'day');
  }

  /**
   * Бизнес-время (учёт смены суток в 05:00):
   * Возвращает момент времени, соответствующий паре (дата B, время T) на оси реального времени,
   * где бизнес-день B начинается в 05:00 локального времени и продолжается до 04:59 следующего календарного дня.
   * Дополнительно, если текущее локальное время до 05:00, вся ось бизнес-дня сдвигается на -24ч,
   * чтобы корректно сравнивать "прошло 3 часа" для ночных рейдов 00:00–04:59.
   */
  static createBusinessDateTime(dateString, timeString) {
    const date = this.parseDate(dateString);
    const time = this.parseTime(timeString);
    if (!date || !time) return null;

    // Создаем базовое время из даты и времени
    let eventMoment = date.clone()
      .hours(time.hours)
      .minutes(time.minutes)
      .seconds(0)
      .milliseconds(0);

    // Если время заказа в промежутке 00:00-04:59, то для целей проверки
    // он относится к следующему календарному дню (так как бизнес-день начинается в 05:00)
    if (eventMoment.hours() < 5) {
      eventMoment.add(1, 'day');
    }

    return eventMoment;
  }
}

module.exports = DateUtils;