require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Загрузка динамической конфигурации
function loadDynamicConfig() {
  const dynamicConfigPath = path.join(__dirname, 'dynamic-config.json');

  try {
    if (fs.existsSync(dynamicConfigPath)) {
      const dynamicConfig = JSON.parse(fs.readFileSync(dynamicConfigPath, 'utf8'));
      console.log('Загружена динамическая конфигурация из файла');
      return dynamicConfig;
    }
  } catch (error) {
    console.warn('Ошибка загрузки динамической конфигурации:', error.message);
  }

  return null;
}

// Для синхронной загрузки используем файл, для асинхронной - Google Sheets
const dynamicConfig = loadDynamicConfig();

// Функция для асинхронной загрузки конфигурации из Google Sheets
async function loadConfigFromSheets() {
  try {
    const ConfigSheetsService = require('../services/configSheetsService');
    const configService = new ConfigSheetsService();
    await configService.initialize();
    const sheetsConfig = await configService.loadConfig();
    console.log('Загружена конфигурация из Google Sheets');
    return sheetsConfig;
  } catch (error) {
    console.warn('Ошибка загрузки конфигурации из Google Sheets:', error.message);
    return null;
  }
}

const config = {
  // Telegram Bot Configuration
  telegram: {
    botToken: process.env.TELEGRAM_BOT_TOKEN,
    chatId: process.env.TELEGRAM_CHAT_ID,
  },

  // Google Sheets Configuration
  googleSheets: {
    clientSpreadsheetId: process.env.GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID,
    scheduleSpreadsheetId: process.env.GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID,
    serviceAccountKey: process.env.GOOGLE_SERVICE_ACCOUNT_KEY,

    // Диапазоны данных в таблице клиентов
    clientsRange: 'A:Z', // Весь лист

    // Колонки в таблице клиентов (индексы начинаются с 0)
    clientColumns: {
      date: 1,        // Столбец B (дата)
      orderId: 4,     // Столбец E (номер заказа)
      time: 19,       // Столбец T (время)
      team: 20,       // Столбец U (выполняющая команда)
      raidType: 21,   // Столбец V (тип рейда)
      accounts: 22,   // Столбец W (количество аккаунтов)
    },

    // Настройки таблицы расписания (внешнее)
    scheduleSheetName: 'Import',
    scheduleRange: 'A1:D304', // Столбцы A-D для расписания (до строки 304)

    // Колонки в таблице расписания (индексы начинаются с 0)
    scheduleColumns: {
      time: 0,        // Столбец A (время)
      raidType: 1,    // Столбец B (название рейда)
      status: 3,      // Столбец D (чекбокс закрытия)
    },

    // Настройки внутреннего расписания (составы)
    internalScheduleSheetName: 'Schedule',
    internalScheduleRange: 'N:P', // Столбцы N-P для внутреннего расписания

    // Колонки во внутреннем расписании (индексы начинаются с 0)
    internalScheduleColumns: {
      team: 0,        // Столбец N (состав)
      time: 1,        // Столбец O (время)
      raidType: 2,    // Столбец P (название рейда)
    },
  },

  // Schedule API Configuration
  scheduleApi: {
    url: 'https://script.google.com/macros/s/AKfycbweyo7Rl0_q3E_yK8v7At0jl-q5JZsWYsMEhtnTWvAOYnjYv21hHSGvzT5ABfSVCGo/exec',
    authorization: '35f05hh19-3c38-4979-ab01-055441321hj4b',
    includeActive: true,
  },

  // Настройки мониторинга
  monitoring: {
    checkIntervalMinutes: dynamicConfig?.monitoring?.checkIntervalMinutes || parseInt(process.env.CHECK_INTERVAL_MINUTES) || 10,
    mythicNotificationDelayMinutes: dynamicConfig?.monitoring?.mythicNotificationDelayMinutes || parseInt(process.env.MYTHIC_NOTIFICATION_DELAY_MINUTES) || 15,
    preRaidCheckMinutes: dynamicConfig?.monitoring?.preRaidCheckMinutes || parseInt(process.env.PRE_RAID_CHECK_MINUTES) || 10,
    accountsReminderMinutes: dynamicConfig?.monitoring?.accountsReminderMinutes || parseInt(process.env.ACCOUNTS_REMINDER_MINUTES) || 30,
    minClientsThreshold: dynamicConfig?.monitoring?.minClientsThreshold || parseInt(process.env.MIN_CLIENTS_THRESHOLD) || 20,
  },

  // Временная зона (все время в таблицах в CET/CEST)
  timezone: process.env.TIMEZONE || 'Europe/Berlin',

  // Допустимые типы рейдов для каждого типа в расписании
  allowedRaidTypes: dynamicConfig?.allowedRaidTypes || {
    'LoU Heroic': ['LoU Heroic', 'LoU Heroic Unsaved','Gallywix Heroic','LoU Single Boss HC','Prototype A.S.M.R.'],
    'LoU Heroic Unsaved': ['LoU Heroic', 'LoU Heroic Unsaved','Gallywix Heroic','LoU Single Boss HC','Prototype A.S.M.R.'],
    'LoU Mythic': ['LoU Mythic', 'LoU Single Boss Mythic','LoU Mythic 8/8 FP 20ppl'],
    'LoU Normal': ['LoU Normal', 'LoU Normal Unsaved','Gallywix Normal','LoU Single Boss NM'],
    'LoU Normal Unsaved': ['LoU Normal', 'LoU Normal Unsaved','Gallywix Normal','LoU Single Boss NM'],
    'MO Normal': ['MO Normal', 'MO Normal Unsaved', 'Dimensius Normal', 'MO Single Boss NM'],
    'MO Normal Unsaved': ['MO Normal', 'MO Normal Unsaved', 'Dimensius Normal', 'MO Single Boss NM'],
    // Добавьте другие типы рейдов по необходимости
  },

  // Лимиты заполненности по командам и сложности (без mythic)
  teamCapacityLimits: dynamicConfig?.teamCapacityLimits || {
    'h2s': {
      normal: 20,
      heroic: 17,
    },
    'a2s': {
      normal: 21,
      heroic: 13,
    },
    'a1': {
      normal: 21,
      heroic: 11,
    },
    'h1': {
      normal: 20,
      heroic: 12,
    },
  },

  // Лимиты заполненности рейдов (по умолчанию, без mythic)
  raidCapacityLimits: {
    default: 20,
    heroic: 25,
    normal: 30,
  },

  // Типы рейдов, которые считаются "ласт боссом"
  lastBossRaidTypes: dynamicConfig?.lastBossRaidTypes || [
    'Gallywix Heroic',
    'Gallywix Normal',
    'Gallywix Mythic'
  ],

  // Шаблоны сообщений
  messages: {
    mythicRaidReminder: (time) =>
      `🔥 Проверь не нужно ли закрыть в расписании мифик рейд - ${time}`,

    raidFullNotification: (raidType, date, time, clientCount) =>
      `✅ Рейд заполнен!\n📅 ${raidType}\n🗓 ${date} ${time}\n👥 Клиентов: ${clientCount}`,

    raidTypeMismatch: (scheduleType, clientType, date, time) =>
      `⚠️ Несоответствие типов рейдов!\n📋 В расписании: ${scheduleType}\n📊 В таблице клиентов: ${clientType}\n🗓 ${date} ${time}`,

    teamMismatch: (raidType, date, time, issues) =>
      `⚠️ Проблемы с командами за 10 минут до рейда!\n📅 ${raidType}\n🗓 ${date} ${time}\n❌ ${issues.join('\n❌ ')}`,

    accountsReminder: (time, totalClients) =>
      `🎮 Через ${config.monitoring.accountsReminderMinutes} минут рейд/рейды, не забудьте пошарить ${totalClients} аккаунтов\n⏰ ${time}`,
  },

  // Настройки логирования
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
  },
};

// Валидация конфигурации
function validateConfig() {
  const required = [
    'telegram.botToken',
    'telegram.chatId',
    'googleSheets.clientSpreadsheetId',
    'googleSheets.serviceAccountKey',
    'scheduleApi.url',
    'scheduleApi.authorization',
  ];

  for (const path of required) {
    const value = path.split('.').reduce((obj, key) => obj?.[key], config);
    if (!value) {
      throw new Error(`Отсутствует обязательная конфигурация: ${path}`);
    }
  }
}

module.exports = { config, validateConfig, loadConfigFromSheets };