const RaidUtils = require('../src/utils/raidUtils');

describe('RaidUtils', () => {
  describe('isMythicRaid', () => {
    test('should detect mythic raids correctly', () => {
      expect(RaidUtils.isMythicRaid('LoU Mythic')).toBe(true);
      expect(RaidUtils.isMythicRaid('LoU Heroic')).toBe(false);
      expect(RaidUtils.isMythicRaid('mythic raid')).toBe(true);
      expect(RaidUtils.isMythicRaid('')).toBe(false);
      expect(RaidUtils.isMythicRaid(null)).toBe(false);
    });
  });

  describe('groupClientsByRaid', () => {
    test('should group clients correctly', () => {
      const clients = [
        { date: '28.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'Team1' },
        { date: '28.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'Team1' },
        { date: '29.07.2025', time: '21:00', raidType: 'LoU Mythic', team: 'Team2' },
      ];

      const groups = RaidUtils.groupClientsByRaid(clients);

      expect(Object.keys(groups)).toHaveLength(2);
      expect(groups['28.07.2025|20:00|LoU Heroic'].clientCount).toBe(2);
      expect(groups['29.07.2025|21:00|LoU Mythic'].clientCount).toBe(1);
    });
  });

  describe('isRaidFull', () => {
    test('should check threshold correctly', () => {
      expect(RaidUtils.isRaidFull(25, 20)).toBe(true);
      expect(RaidUtils.isRaidFull(15, 20)).toBe(false);
    });
  });

  describe('normalizeTeamName', () => {
    test('should normalize team names correctly', () => {
      expect(RaidUtils.normalizeTeamName('H2S')).toBe('h2s');
      expect(RaidUtils.normalizeTeamName(' A2S ')).toBe('a2s');
      expect(RaidUtils.normalizeTeamName('')).toBe('');
      expect(RaidUtils.normalizeTeamName(null)).toBe('');
    });
  });

  describe('checkTeamAssignments', () => {
    test('should find empty teams', () => {
      const clients = [
        { team: 'h2s', rowNumber: 1 },
        { team: '', rowNumber: 2 },
        { team: null, rowNumber: 3 },
        { team: '  ', rowNumber: 4 },
      ];

      const issues = RaidUtils.checkTeamAssignments({}, clients);
      expect(issues).toHaveLength(3);
      expect(issues[0]).toContain('строка 2');
      expect(issues[1]).toContain('строка 3');
      expect(issues[2]).toContain('строка 4');
    });
  });

  describe('isRaidTypeAllowed', () => {
    test('should check raid type compatibility', () => {
      // Точное совпадение
      expect(RaidUtils.isRaidTypeAllowed('LoU Heroic', 'LoU Heroic')).toBe(true);
      
      // Mythic рейды должны совпадать
      expect(RaidUtils.isRaidTypeAllowed('LoU Mythic', 'LoU Single Boss Mythic')).toBe(true);
      
      // Разные типы не должны совпадать
      expect(RaidUtils.isRaidTypeAllowed('LoU Heroic', 'LoU Normal')).toBe(false);
    });
  });

  describe('findRaidTypeMismatches', () => {
    test('should find mismatched raid types', () => {
      const scheduleRaids = [
        { date: '28.07.2025', time: '20:00', raidType: 'LoU Heroic' }
      ];
      
      const clientGroups = {
        '28.07.2025|20:00|LoU Normal': {
          date: '28.07.2025',
          time: '20:00',
          raidType: 'LoU Normal',
          clientCount: 5
        }
      };

      const mismatches = RaidUtils.findRaidTypeMismatches(scheduleRaids, clientGroups);
      expect(mismatches).toHaveLength(1);
      expect(mismatches[0].clientRaidType).toBe('LoU Normal');
      expect(mismatches[0].scheduleRaidType).toBe('LoU Heroic');
    });
  });
});
