# 🚀 Установка WoW Raid Bot на VPS

## 📋 Требования

- VPS с Ubuntu 20.04+ (или любой Linux)
- Node.js 18+
- Git
- PM2 (для автозапуска)

## ⚡ Быстрая установка

### 1. Подключение к VPS

```bash
ssh root@your-server-ip
```

### 2. Установка Node.js

```bash
# Обновление системы
apt update && apt upgrade -y

# Установка Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Проверка
node --version
npm --version
```

### 3. Установка PM2

```bash
npm install -g pm2
```

### 4. Клонирование проекта

```bash
# Переход в домашнюю директорию
cd /home

# Клонирование репозитория
git clone https://github.com/your-username/reminderbot.git
cd reminderbot

# Установка зависимостей
npm install
```

### 5. Настройка переменных окружения

```bash
# Создание файла .env
cp .env.example .env
nano .env
```

**Заполните .env файл:**

```bash
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# Google Sheets
GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID=1IPd9yEJzx_PTOVFXPmNpgL6zYINVKwBKTFvReMmTKNY
GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID=1dzPdrJGulpXPlTEhsoiXtGQCwGQJo8d60Wlv4igBQOM
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}

# Web Interface (опционально)
ADMIN_PASSWORD=your_secure_password
WEB_PORT=3000

# System
MODE=both
USE_GOOGLE_SHEETS_CONFIG=true
TIMEZONE=Europe/Berlin
```

### 6. Инициализация Google Sheets

```bash
npm run init-config
```

### 7. Запуск с PM2

```bash
# Запуск бота
pm2 start index.js --name "wow-raid-bot"

# Автозапуск при перезагрузке
pm2 startup
pm2 save

# Проверка статуса
pm2 status
pm2 logs wow-raid-bot
```

## 🔧 Управление ботом

### Основные команды PM2

```bash
# Статус всех процессов
pm2 status

# Логи бота
pm2 logs wow-raid-bot

# Перезапуск
pm2 restart wow-raid-bot

# Остановка
pm2 stop wow-raid-bot

# Удаление из PM2
pm2 delete wow-raid-bot

# Мониторинг в реальном времени
pm2 monit
```

### Обновление бота

```bash
cd /home/<USER>

# Остановка
pm2 stop wow-raid-bot

# Обновление кода
git pull origin main
npm install

# Запуск
pm2 start wow-raid-bot
```

## 🌐 Веб-интерфейс

Если включен режим `both` или `web`, веб-интерфейс будет доступен по адресу:

```
http://your-server-ip:3000/admin
```

### Настройка Nginx (опционально)

```bash
# Установка Nginx
apt install nginx -y

# Создание конфигурации
nano /etc/nginx/sites-available/wow-raid-bot
```

**Конфигурация Nginx:**

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Активация конфигурации
ln -s /etc/nginx/sites-available/wow-raid-bot /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

## 🔒 Безопасность

### Настройка Firewall

```bash
# Установка UFW
apt install ufw -y

# Базовые правила
ufw default deny incoming
ufw default allow outgoing

# Разрешение SSH и HTTP
ufw allow ssh
ufw allow 80
ufw allow 443

# Включение firewall
ufw enable
```

### SSL сертификат (Certbot)

```bash
# Установка Certbot
apt install certbot python3-certbot-nginx -y

# Получение сертификата
certbot --nginx -d your-domain.com

# Автообновление
crontab -e
# Добавить: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Мониторинг

### Логи системы

```bash
# Логи PM2
pm2 logs wow-raid-bot --lines 100

# Системные логи
journalctl -u nginx -f
```

### Автоматические бэкапы

```bash
# Создание скрипта бэкапа
nano /home/<USER>
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>"
mkdir -p $BACKUP_DIR

# Бэкап конфигурации
cp /home/<USER>/.env $BACKUP_DIR/env_$DATE.backup

# Бэкап кода (если есть изменения)
cd /home/<USER>
tar -czf $BACKUP_DIR/reminderbot_$DATE.tar.gz .

# Удаление старых бэкапов (старше 30 дней)
find $BACKUP_DIR -name "*.backup" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

```bash
# Права на выполнение
chmod +x /home/<USER>

# Добавление в cron (ежедневно в 2:00)
crontab -e
# Добавить: 0 2 * * * /home/<USER>
```

## ✅ Проверка работы

### 1. Статус процессов

```bash
pm2 status
```

### 2. Тест Telegram команд

В Telegram чате отправьте:
- `/help`
- `/test`
- `/filled`

### 3. Веб-интерфейс

Откройте в браузере:
```
http://your-server-ip:3000/admin
```

## 🆘 Решение проблем

### Бот не запускается

```bash
# Проверка логов
pm2 logs wow-raid-bot

# Проверка переменных окружения
npm run check-env

# Ручной запуск для отладки
node index.js
```

### Веб-интерфейс недоступен

```bash
# Проверка порта
netstat -tlnp | grep 3000

# Проверка Nginx
nginx -t
systemctl status nginx
```

### Google Sheets не работает

```bash
# Проверка сервисного аккаунта
npm run check-env

# Переинициализация
npm run init-config
```

---

**🎉 Готово! Ваш WoW Raid Bot работает на VPS 24/7!**
