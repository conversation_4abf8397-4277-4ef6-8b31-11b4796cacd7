const DateUtils = require('../src/utils/dateUtils');
const RaidUtils = require('../src/utils/raidUtils');

console.log('🧪 Запуск тестов утилит...\n');

// Простая функция для тестирования
function test(name, fn) {
  try {
    fn();
    console.log(`✅ ${name}`);
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
  }
}

function expect(actual) {
  return {
    toBe: (expected) => {
      if (actual !== expected) {
        throw new Error(`Expected ${expected}, got ${actual}`);
      }
    },
    toBeTruthy: () => {
      if (!actual) {
        throw new Error(`Expected truthy value, got ${actual}`);
      }
    },
    toBeNull: () => {
      if (actual !== null) {
        throw new Error(`Expected null, got ${actual}`);
      }
    },
    toEqual: (expected) => {
      if (JSON.stringify(actual) !== JSON.stringify(expected)) {
        throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`);
      }
    },
    toHaveLength: (expected) => {
      if (actual.length !== expected) {
        throw new Error(`Expected length ${expected}, got ${actual.length}`);
      }
    }
  };
}

// Тесты DateUtils
console.log('📅 Тестирование DateUtils:');

test('parseDate should parse various date formats', () => {
  expect(DateUtils.parseDate('28.07.2025')).toBeTruthy();
  expect(DateUtils.parseDate('28/07/2025')).toBeTruthy();
  expect(DateUtils.parseDate('2025-07-28')).toBeTruthy();
  expect(DateUtils.parseDate('invalid')).toBeNull();
});

test('parseTime should parse various time formats', () => {
  expect(DateUtils.parseTime('20:30')).toEqual({ hours: 20, minutes: 30 });
  expect(DateUtils.parseTime('8:30')).toEqual({ hours: 8, minutes: 30 });
  expect(DateUtils.parseTime('invalid')).toBeNull();
});

test('createDateTime should create valid moment', () => {
  const dateTime = DateUtils.createDateTime('28.07.2025', '20:30');
  expect(dateTime).toBeTruthy();
  expect(dateTime.isValid()).toBe(true);
});

// Тесты RaidUtils
console.log('\n⚔️ Тестирование RaidUtils:');

test('isMythicRaid should detect mythic raids', () => {
  expect(RaidUtils.isMythicRaid('LoU Mythic')).toBe(true);
  expect(RaidUtils.isMythicRaid('Nerub-ar Palace Mythic')).toBe(true);
  expect(RaidUtils.isMythicRaid('LoU Heroic')).toBe(false);
  expect(RaidUtils.isMythicRaid('')).toBe(false);
});

test('groupClientsByRaid should group clients correctly', () => {
  const clients = [
    { date: '28.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'Team1' },
    { date: '28.07.2025', time: '20:00', raidType: 'LoU Heroic', team: 'Team1' },
    { date: '29.07.2025', time: '21:00', raidType: 'LoU Mythic', team: 'Team2' },
  ];

  const groups = RaidUtils.groupClientsByRaid(clients);

  expect(Object.keys(groups)).toHaveLength(2);
  expect(groups['28.07.2025|20:00|LoU Heroic'].clientCount).toBe(2);
  expect(groups['29.07.2025|21:00|LoU Mythic'].clientCount).toBe(1);
});

test('isRaidFull should check threshold correctly', () => {
  expect(RaidUtils.isRaidFull(25, 20)).toBe(true);
  expect(RaidUtils.isRaidFull(15, 20)).toBe(false);
});

// Тесты для новой функциональности проверки команд
describe('Team validation', () => {
  test('normalizeTeamName should normalize team names correctly', () => {
    expect(RaidUtils.normalizeTeamName('H2S')).toBe('h2s');
    expect(RaidUtils.normalizeTeamName(' A2S ')).toBe('a2s');
    expect(RaidUtils.normalizeTeamName('')).toBe('');
    expect(RaidUtils.normalizeTeamName(null)).toBe('');
  });

  test('checkTeamAssignments should find empty teams', () => {
    const clients = [
      { team: 'h2s', rowNumber: 1 },
      { team: '', rowNumber: 2 },
      { team: null, rowNumber: 3 },
      { team: '  ', rowNumber: 4 },
    ];

    const issues = RaidUtils.checkTeamAssignments({}, clients);
    expect(issues).toHaveLength(3);
    expect(issues[0]).toContain('строка 2');
    expect(issues[1]).toContain('строка 3');
    expect(issues[2]).toContain('строка 4');
  });
});

console.log('\n🎉 Все тесты утилит прошли успешно!');