// Глобальная переменная для хранения текущей конфигурации
let currentConfig = {};

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    loadConfig();
});

/**
 * Загрузка конфигурации с сервера
 */
async function loadConfig() {
    const loading = document.getElementById('loading');
    const configForm = document.getElementById('configForm');
    
    loading.style.display = 'block';
    configForm.style.display = 'none';
    
    try {
        const response = await fetch('/api/config');
        const result = await response.json();
        
        if (result.success) {
            currentConfig = result.config;
            populateForm(currentConfig);
            configForm.style.display = 'block';
        } else {
            showAlert('Ошибка загрузки конфигурации: ' + result.message, 'error');
        }
    } catch (error) {
        showAlert('Ошибка подключения к серверу', 'error');
        console.error('Ошибка загрузки конфигурации:', error);
    } finally {
        loading.style.display = 'none';
    }
}

/**
 * Заполнение формы данными конфигурации
 */
function populateForm(config) {
    // Настройки мониторинга
    document.getElementById('checkInterval').value = config.monitoring?.checkIntervalMinutes || 10;
    document.getElementById('mythicDelay').value = config.monitoring?.mythicNotificationDelayMinutes || 15;
    document.getElementById('preRaidCheck').value = config.monitoring?.preRaidCheckMinutes || 10;
    document.getElementById('minClients').value = config.monitoring?.minClientsThreshold || 20;

    // Заполняем динамические поля
    populateAllowedRaidTypes(config.allowedRaidTypes || {});
    populateTeamCapacityLimits(config.teamCapacityLimits || {});
    populateLastBossTypes(config.lastBossRaidTypes || []);
}

/**
 * Заполнение допустимых типов рейдов
 */
function populateAllowedRaidTypes(allowedRaidTypes) {
    const container = document.getElementById('allowedRaidTypesContainer');
    container.innerHTML = '';

    Object.entries(allowedRaidTypes).forEach(([scheduleType, allowedTypes]) => {
        addRaidTypeMapping(scheduleType, allowedTypes);
    });
}

/**
 * Добавление нового маппинга типов рейдов
 */
function addRaidTypeMapping(scheduleType = '', allowedTypes = []) {
    const container = document.getElementById('allowedRaidTypesContainer');
    const fieldId = 'raidType_' + Date.now();

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'dynamic-field';
    fieldDiv.id = fieldId;

    fieldDiv.innerHTML = `
        <div class="dynamic-field-header">
            <span class="dynamic-field-title">Тип в расписании</span>
            <button type="button" class="remove-btn" onclick="removeField('${fieldId}')">Удалить</button>
        </div>
        <div class="form-group">
            <label>Название в расписании:</label>
            <input type="text" class="schedule-type" value="${scheduleType}" placeholder="LoU Heroic">
        </div>
        <div class="form-group">
            <label>Допустимые типы в таблице клиентов:</label>
            <div class="allowed-types-list" id="allowedList_${fieldId}">
                ${allowedTypes.map(type => `
                    <div class="allowed-type-tag">
                        ${type}
                        <button type="button" class="remove-tag" onclick="removeAllowedType(this)">×</button>
                    </div>
                `).join('')}
            </div>
            <div class="add-allowed-type">
                <input type="text" placeholder="Введите тип рейда" onkeypress="if(event.key==='Enter') addAllowedType(this)">
                <button type="button" onclick="addAllowedType(this.previousElementSibling)">Добавить</button>
            </div>
        </div>
    `;

    container.appendChild(fieldDiv);
}

/**
 * Добавление допустимого типа рейда
 */
function addAllowedType(input) {
    const value = input.value.trim();
    if (!value) return;

    const fieldDiv = input.closest('.dynamic-field');
    const allowedList = fieldDiv.querySelector('.allowed-types-list');

    const tag = document.createElement('div');
    tag.className = 'allowed-type-tag';
    tag.innerHTML = `
        ${value}
        <button type="button" class="remove-tag" onclick="removeAllowedType(this)">×</button>
    `;

    allowedList.appendChild(tag);
    input.value = '';
}

/**
 * Удаление допустимого типа рейда
 */
function removeAllowedType(button) {
    button.parentElement.remove();
}

/**
 * Сбор данных о допустимых типах рейдов
 */
function collectAllowedRaidTypes() {
    const result = {};
    const container = document.getElementById('allowedRaidTypesContainer');

    container.querySelectorAll('.dynamic-field').forEach(field => {
        const scheduleType = field.querySelector('.schedule-type').value.trim();
        if (!scheduleType) return;

        const allowedTypes = [];
        field.querySelectorAll('.allowed-type-tag').forEach(tag => {
            const text = tag.textContent.replace('×', '').trim();
            if (text) allowedTypes.push(text);
        });

        result[scheduleType] = allowedTypes;
    });

    return result;
}

/**
 * Заполнение лимитов команд
 */
function populateTeamCapacityLimits(teamCapacityLimits) {
    const container = document.getElementById('teamCapacityContainer');
    container.innerHTML = '';

    Object.entries(teamCapacityLimits).forEach(([teamName, limits]) => {
        addTeamCapacity(teamName, limits.normal || 20, limits.heroic || 15);
    });
}

/**
 * Добавление новой команды
 */
function addTeamCapacity(teamName = '', normalLimit = 20, heroicLimit = 15) {
    const container = document.getElementById('teamCapacityContainer');
    const fieldId = 'team_' + Date.now();

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'dynamic-field';
    fieldDiv.id = fieldId;

    fieldDiv.innerHTML = `
        <div class="dynamic-field-header">
            <span class="dynamic-field-title">Команда</span>
            <button type="button" class="remove-btn" onclick="removeField('${fieldId}')">Удалить</button>
        </div>
        <div class="form-group">
            <label>Название команды:</label>
            <input type="text" class="team-name" value="${teamName}" placeholder="h2s">
        </div>
        <div class="capacity-inputs">
            <div class="form-group">
                <label>Лимит Normal:</label>
                <input type="number" class="normal-limit" value="${normalLimit}" min="1" max="50">
            </div>
            <div class="form-group">
                <label>Лимит Heroic:</label>
                <input type="number" class="heroic-limit" value="${heroicLimit}" min="1" max="50">
            </div>
        </div>
    `;

    container.appendChild(fieldDiv);
}

/**
 * Сбор данных о лимитах команд
 */
function collectTeamCapacityLimits() {
    const result = {};
    const container = document.getElementById('teamCapacityContainer');

    container.querySelectorAll('.dynamic-field').forEach(field => {
        const teamName = field.querySelector('.team-name').value.trim();
        if (!teamName) return;

        const normalLimit = parseInt(field.querySelector('.normal-limit').value);
        const heroicLimit = parseInt(field.querySelector('.heroic-limit').value);

        result[teamName] = {
            normal: normalLimit,
            heroic: heroicLimit
        };
    });

    return result;
}

/**
 * Заполнение типов "ласт босс"
 */
function populateLastBossTypes(lastBossTypes) {
    const container = document.getElementById('lastBossContainer');
    container.innerHTML = '';

    lastBossTypes.forEach(type => {
        addLastBossType(type);
    });
}

/**
 * Добавление нового типа "ласт босс"
 */
function addLastBossType(typeName = '') {
    const container = document.getElementById('lastBossContainer');
    const fieldId = 'lastBoss_' + Date.now();

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'dynamic-field';
    fieldDiv.id = fieldId;

    fieldDiv.innerHTML = `
        <div class="dynamic-field-header">
            <span class="dynamic-field-title">Тип рейда "Ласт босс"</span>
            <button type="button" class="remove-btn" onclick="removeField('${fieldId}')">Удалить</button>
        </div>
        <div class="form-group">
            <label>Название типа рейда:</label>
            <input type="text" class="last-boss-type" value="${typeName}" placeholder="Gallywix Heroic">
        </div>
    `;

    container.appendChild(fieldDiv);
}

/**
 * Сбор данных о типах "ласт босс"
 */
function collectLastBossTypes() {
    const result = [];
    const container = document.getElementById('lastBossContainer');

    container.querySelectorAll('.dynamic-field').forEach(field => {
        const typeName = field.querySelector('.last-boss-type').value.trim();
        if (typeName) {
            result.push(typeName);
        }
    });

    return result;
}

/**
 * Удаление поля
 */
function removeField(fieldId) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.remove();
    }
}

/**
 * Сохранение конфигурации
 */
async function saveConfig() {
    try {
        const newConfig = {
            monitoring: {
                checkIntervalMinutes: parseInt(document.getElementById('checkInterval').value),
                mythicNotificationDelayMinutes: parseInt(document.getElementById('mythicDelay').value),
                preRaidCheckMinutes: parseInt(document.getElementById('preRaidCheck').value),
                minClientsThreshold: parseInt(document.getElementById('minClients').value)
            },
            allowedRaidTypes: collectAllowedRaidTypes(),
            teamCapacityLimits: collectTeamCapacityLimits(),
            lastBossRaidTypes: collectLastBossTypes()
        };
        
        // Валидация
        if (!validateConfig(newConfig)) {
            return;
        }
        
        const response = await fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(newConfig),
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentConfig = newConfig;
            showAlert('Конфигурация успешно сохранена! Перезапустите бота для применения изменений.', 'success');
        } else {
            showAlert('Ошибка сохранения: ' + result.message, 'error');
        }
    } catch (error) {
        showAlert('Ошибка сохранения конфигурации', 'error');
        console.error('Ошибка сохранения:', error);
    }
}

/**
 * Валидация конфигурации
 */
function validateConfig(config) {
    // Проверка настроек мониторинга
    if (!config.monitoring) {
        showAlert('Отсутствуют настройки мониторинга', 'error');
        return false;
    }

    const { checkIntervalMinutes, mythicNotificationDelayMinutes, preRaidCheckMinutes, minClientsThreshold } = config.monitoring;

    if (isNaN(checkIntervalMinutes) || checkIntervalMinutes < 1 || checkIntervalMinutes > 60) {
        showAlert('Интервал проверки должен быть числом от 1 до 60 минут', 'error');
        return false;
    }

    if (isNaN(mythicNotificationDelayMinutes) || mythicNotificationDelayMinutes < 1 || mythicNotificationDelayMinutes > 60) {
        showAlert('Задержка уведомлений должна быть числом от 1 до 60 минут', 'error');
        return false;
    }

    if (isNaN(preRaidCheckMinutes) || preRaidCheckMinutes < 1 || preRaidCheckMinutes > 60) {
        showAlert('Время проверки команд должно быть числом от 1 до 60 минут', 'error');
        return false;
    }

    if (isNaN(minClientsThreshold) || minClientsThreshold < 1 || minClientsThreshold > 50) {
        showAlert('Порог клиентов должен быть числом от 1 до 50', 'error');
        return false;
    }

    // Проверка типов рейдов
    if (!config.allowedRaidTypes || typeof config.allowedRaidTypes !== 'object') {
        showAlert('Неверный формат допустимых типов рейдов', 'error');
        return false;
    }

    // Проверка лимитов команд
    if (!config.teamCapacityLimits || typeof config.teamCapacityLimits !== 'object') {
        showAlert('Неверный формат лимитов команд', 'error');
        return false;
    }

    // Проверка лимитов команд на корректность значений
    for (const [teamName, limits] of Object.entries(config.teamCapacityLimits)) {
        if (!teamName.trim()) {
            showAlert('Название команды не может быть пустым', 'error');
            return false;
        }
        if (isNaN(limits.normal) || limits.normal < 1 || limits.normal > 50) {
            showAlert(`Лимит Normal для команды ${teamName} должен быть от 1 до 50`, 'error');
            return false;
        }
        if (isNaN(limits.heroic) || limits.heroic < 1 || limits.heroic > 50) {
            showAlert(`Лимит Heroic для команды ${teamName} должен быть от 1 до 50`, 'error');
            return false;
        }
    }

    // Проверка типов ласт босса
    if (!Array.isArray(config.lastBossRaidTypes)) {
        showAlert('Типы рейдов "ласт босс" должны быть массивом', 'error');
        return false;
    }

    return true;
}

/**
 * Показ уведомления
 */
function showAlert(message, type) {
    const alertsContainer = document.getElementById('alerts');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    alertsContainer.appendChild(alertDiv);
    
    // Автоматическое скрытие через 5 секунд
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

/**
 * Выход из системы
 */
async function logout() {
    try {
        await fetch('/admin/logout', { method: 'POST' });
        window.location.reload();
    } catch (error) {
        console.error('Ошибка выхода:', error);
        window.location.reload();
    }
}

// Удалена функция форматирования JSON - больше не нужна

/**
 * Горячие клавиши
 */
document.addEventListener('keydown', (e) => {
    // Ctrl+S для сохранения
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveConfig();
    }
    
    // Ctrl+R для перезагрузки конфигурации
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        loadConfig();
    }
});
