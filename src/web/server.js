const express = require('express');
const session = require('express-session');
const path = require('path');
const fs = require('fs').promises;
const { config } = require('../config/config');
const ConfigSheetsService = require('../services/configSheetsService');

/**
 * Веб-сервер для управления конфигурацией бота
 */
class ConfigWebServer {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    this.configPath = path.join(__dirname, '../config/dynamic-config.json');
    this.configSheetsService = new ConfigSheetsService();
    this.useGoogleSheets = process.env.USE_GOOGLE_SHEETS_CONFIG === 'true';
  }

  /**
   * Инициализация веб-сервера
   */
  async initialize() {
    // Настройка middleware
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.join(__dirname, 'public')));

    // Настройка сессий
    this.app.use(session({
      secret: process.env.SESSION_SECRET || 'wow-raid-bot-secret',
      resave: true,
      saveUninitialized: true,
      cookie: {
        secure: false, // Отключаем secure для локальной разработки
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24 часа
      }
    }));

    // Инициализируем Google Sheets сервис если используем его
    if (this.useGoogleSheets) {
      try {
        await this.configSheetsService.initialize();
        console.log('Google Sheets конфигурация инициализирована');
      } catch (error) {
        console.error('Ошибка инициализации Google Sheets конфигурации:', error.message);
        this.useGoogleSheets = false; // Fallback к файловой системе
      }
    }

    // Настройка маршрутов
    this.setupRoutes();

    console.log('Веб-сервер конфигурации инициализирован');
  }

  /**
   * Настройка маршрутов
   */
  setupRoutes() {
    // Главная страница - редирект на админку
    this.app.get('/', (req, res) => {
      res.redirect('/admin');
    });

    // Страница входа
    this.app.get('/admin', (req, res) => {
      console.log('GET /admin - ID сессии:', req.sessionID);
      console.log('GET /admin - сессия:', req.session);
      console.log('GET /admin - проверка аутентификации:', req.session.authenticated ? 'АУТЕНТИФИЦИРОВАН' : 'НЕ АУТЕНТИФИЦИРОВАН');

      // Заголовки для предотвращения кэширования
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      if (req.session.authenticated) {
        console.log('Отправка admin.html');
        res.sendFile(path.join(__dirname, 'public', 'admin.html'));
      } else {
        console.log('Отправка login.html');
        res.sendFile(path.join(__dirname, 'public', 'login.html'));
      }
    });

    // Аутентификация
    this.app.post('/admin/login', (req, res) => {
      const { password } = req.body;

      console.log('Попытка входа с паролем:', password ? '[СКРЫТ]' : 'ПУСТОЙ');
      console.log('Ожидаемый пароль:', this.adminPassword ? '[УСТАНОВЛЕН]' : 'НЕ УСТАНОВЛЕН');
      console.log('ID сессии до аутентификации:', req.sessionID);
      console.log('Сессия до аутентификации:', req.session);

      if (password === this.adminPassword) {
        req.session.authenticated = true;
        console.log('Успешная аутентификация');
        console.log('ID сессии после аутентификации:', req.sessionID);
        console.log('Сессия после аутентификации:', req.session);

        // Принудительно сохраняем сессию
        req.session.save((err) => {
          if (err) {
            console.error('Ошибка сохранения сессии:', err);
            return res.status(500).json({ success: false, message: 'Ошибка сохранения сессии' });
          }
          console.log('Сессия успешно сохранена');
          res.json({ success: true });
        });
      } else {
        console.log('Неверный пароль');
        res.status(401).json({ success: false, message: 'Неверный пароль' });
      }
    });

    // Выход
    this.app.post('/admin/logout', (req, res) => {
      req.session.destroy();
      res.json({ success: true });
    });

    // Middleware для проверки аутентификации
    const requireAuth = (req, res, next) => {
      if (!req.session.authenticated) {
        return res.status(401).json({ success: false, message: 'Требуется аутентификация' });
      }
      next();
    };

    // API для получения текущей конфигурации
    this.app.get('/api/config', requireAuth, async (req, res) => {
      try {
        const dynamicConfig = await this.loadDynamicConfig();
        res.json({
          success: true,
          config: dynamicConfig
        });
      } catch (error) {
        console.error('Ошибка загрузки конфигурации:', error);
        res.status(500).json({ 
          success: false, 
          message: 'Ошибка загрузки конфигурации' 
        });
      }
    });

    // API для сохранения конфигурации
    this.app.post('/api/config', requireAuth, async (req, res) => {
      try {
        const newConfig = req.body;
        await this.saveDynamicConfig(newConfig);
        
        res.json({
          success: true,
          message: 'Конфигурация успешно сохранена'
        });
      } catch (error) {
        console.error('Ошибка сохранения конфигурации:', error);
        res.status(500).json({ 
          success: false, 
          message: 'Ошибка сохранения конфигурации' 
        });
      }
    });

    // API для перезапуска бота (если нужно)
    this.app.post('/api/restart', requireAuth, (req, res) => {
      res.json({
        success: true,
        message: 'Для применения изменений перезапустите бота'
      });
    });
  }

  /**
   * Загрузка динамической конфигурации
   */
  async loadDynamicConfig() {
    try {
      if (this.useGoogleSheets) {
        console.log('Загрузка конфигурации из Google Sheets...');
        return await this.configSheetsService.loadConfig();
      } else {
        console.log('Загрузка конфигурации из файла...');
        const data = await fs.readFile(this.configPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn('Ошибка загрузки конфигурации:', error.message);
      // Если не удалось загрузить, создаем конфигурацию по умолчанию
      const defaultConfig = this.getDefaultDynamicConfig();

      if (this.useGoogleSheets) {
        try {
          await this.configSheetsService.saveConfig(defaultConfig);
          console.log('Создана конфигурация по умолчанию в Google Sheets');
        } catch (saveError) {
          console.error('Ошибка создания конфигурации по умолчанию:', saveError.message);
        }
      } else {
        await this.saveDynamicConfig(defaultConfig);
      }

      return defaultConfig;
    }
  }

  /**
   * Сохранение динамической конфигурации
   */
  async saveDynamicConfig(configData) {
    if (this.useGoogleSheets) {
      console.log('Сохранение конфигурации в Google Sheets...');
      await this.configSheetsService.saveConfig(configData);
    } else {
      console.log('Сохранение конфигурации в файл...');
      await fs.writeFile(this.configPath, JSON.stringify(configData, null, 2), 'utf8');
    }
  }

  /**
   * Получение конфигурации по умолчанию
   */
  getDefaultDynamicConfig() {
    return {
      monitoring: {
        checkIntervalMinutes: config.monitoring.checkIntervalMinutes,
        mythicNotificationDelayMinutes: config.monitoring.mythicNotificationDelayMinutes,
        preRaidCheckMinutes: config.monitoring.preRaidCheckMinutes,
        minClientsThreshold: config.monitoring.minClientsThreshold
      },
      allowedRaidTypes: config.allowedRaidTypes,
      teamCapacityLimits: config.teamCapacityLimits,
      lastBossRaidTypes: config.lastBossRaidTypes
    };
  }

  /**
   * Запуск сервера
   */
  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`Веб-сервер конфигурации запущен на порту ${this.port}`);
        console.log(`Админ-панель доступна по адресу: http://localhost:${this.port}/admin`);
        resolve();
      });
    });
  }

  /**
   * Остановка сервера
   */
  stop() {
    if (this.server) {
      this.server.close();
      console.log('Веб-сервер конфигурации остановлен');
    }
  }
}

module.exports = ConfigWebServer;
