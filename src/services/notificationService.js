const { Telegraf } = require('telegraf');
const { config } = require('../config/config');

/**
 * Сервис для отправки уведомлений через Telegram
 */
class NotificationService {
  constructor() {
    this.bot = null;
    this.initialized = false;
    this.sentMessages = new Set(); // Для предотвращения дублирования сообщений
  }

  /**
   * Инициализация Telegram бота
   */
  async initialize() {
    try {
      if (!config.telegram.botToken) {
        throw new Error('Не указан токен Telegram бота');
      }

      this.bot = new Telegraf(config.telegram.botToken);

      // Проверяем подключение
      const botInfo = await this.bot.telegram.getMe();
      console.log(`Telegram бот инициализирован: @${botInfo.username}`);

      this.initialized = true;
    } catch (error) {
      console.error('Ошибка инициализации Telegram бота:', error.message);
      throw error;
    }
  }

  /**
   * Отправка уведомления в чат
   */
  async sendNotification(message, options = {}) {
    if (!this.initialized) {
      console.error('NotificationService не инициализирован');
      return false;
    }

    try {
      // Создаем хеш сообщения для предотвращения дублирования
      const messageHash = this._createMessageHash(message);

      // Проверяем, не отправляли ли мы это сообщение недавно (в течение 5 минут)
      if (this.sentMessages.has(messageHash)) {
        console.log('Сообщение уже было отправлено недавно, пропускаем');
        return false;
      }

      const chatId = options.chatId || config.telegram.chatId;

      if (!chatId) {
        throw new Error('Не указан ID чата для отправки сообщения');
      }

      // Отправляем сообщение
      await this.bot.telegram.sendMessage(chatId, message, {
        parse_mode: 'HTML',
        disable_web_page_preview: true,
        ...options.telegramOptions,
      });

      // Добавляем хеш в множество отправленных сообщений
      this.sentMessages.add(messageHash);

      // Удаляем хеш через 5 минут
      setTimeout(() => {
        this.sentMessages.delete(messageHash);
      }, 5 * 60 * 1000);

      console.log('Уведомление отправлено успешно');
      return true;
    } catch (error) {
      console.error('Ошибка отправки уведомления:', error.message);
      return false;
    }
  }

  /**
   * Отправка уведомления с повтором при ошибке
   */
  async sendNotificationWithRetry(message, options = {}, maxRetries = 3) {
    let attempt = 1;

    while (attempt <= maxRetries) {
      const success = await this.sendNotification(message, options);

      if (success) {
        return true;
      }

      if (attempt < maxRetries) {
        console.log(`Попытка ${attempt} не удалась, повторяем через 30 секунд...`);
        await this._sleep(30000); // Ждем 30 секунд перед повтором
      }

      attempt++;
    }

    console.error(`Не удалось отправить уведомление после ${maxRetries} попыток`);
    return false;
  }

  /**
   * Отправка тестового сообщения
   */
  async sendTestMessage() {
    const message = `🤖 Тестовое сообщение от WoW Raid Reminder Bot\n⏰ ${new Date().toLocaleString('ru-RU', { timeZone: config.timezone })}`;
    return await this.sendNotification(message);
  }

  /**
   * Создание хеша сообщения для предотвращения дублирования
   */
  _createMessageHash(message) {
    // Простой хеш на основе содержимого сообщения
    let hash = 0;
    for (let i = 0; i < message.length; i++) {
      const char = message.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Преобразуем в 32-битное число
    }
    return hash.toString();
  }

  /**
   * Вспомогательная функция для задержки
   */
  _sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Получение информации о боте
   */
  async getBotInfo() {
    if (!this.initialized) {
      return null;
    }

    try {
      return await this.bot.telegram.getMe();
    } catch (error) {
      console.error('Ошибка получения информации о боте:', error.message);
      return null;
    }
  }

  /**
   * Проверка доступности чата
   */
  async checkChatAccess(chatId = null) {
    if (!this.initialized) {
      return false;
    }

    try {
      const targetChatId = chatId || config.telegram.chatId;
      const chat = await this.bot.telegram.getChat(targetChatId);
      console.log(`Доступ к чату подтвержден: ${chat.title || chat.first_name || targetChatId}`);
      return true;
    } catch (error) {
      console.error('Ошибка доступа к чату:', error.message);
      return false;
    }
  }

  /**
   * Получение статистики сервиса
   */
  getStats() {
    return {
      initialized: this.initialized,
      sentMessagesCount: this.sentMessages.size,
    };
  }
}

module.exports = NotificationService;