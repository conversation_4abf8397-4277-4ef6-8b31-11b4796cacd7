const { config } = require('../src/config/config');
const SheetsService = require('../src/services/sheetsService');

describe('Piloted Command Tests', () => {
  let sheetsService;

  beforeEach(() => {
    sheetsService = new SheetsService();
    // Мокаем инициализацию
    sheetsService.initialized = true;
  });

  describe('Configuration', () => {
    test('should have accounts column configuration', () => {
      expect(config.googleSheets.clientColumns.accounts).toBe(22);
    });
  });

  describe('SheetsService', () => {
    test('should have getUpcomingRaidsWithAccounts method', () => {
      expect(typeof sheetsService.getUpcomingRaidsWithAccounts).toBe('function');
    });

    test('should have isRaidInFuture method', () => {
      expect(typeof sheetsService.isRaidInFuture).toBe('function');
    });
  });

  describe('Time Filtering', () => {
    test('should filter out past raids', () => {
      // Текущее время: 15.08.2025 20:20
      const currentDate = '15.08.2025';
      const currentTime = '20:20';
      
      // Рейд в прошлом (вчера)
      expect(sheetsService.isRaidInFuture('14.08.2025', '10:00', currentDate, currentTime)).toBe(false);
      
      // Рейд в прошлом (сегодня, но раньше)
      expect(sheetsService.isRaidInFuture('15.08.2025', '15:00', currentDate, currentTime)).toBe(false);
      
      // Рейд в будущем (сегодня, но позже)
      expect(sheetsService.isRaidInFuture('15.08.2025', '22:00', currentDate, currentTime)).toBe(true);
      
      // Рейд в будущем (завтра)
      expect(sheetsService.isRaidInFuture('16.08.2025', '10:00', currentDate, currentTime)).toBe(true);
    });

    test('should handle edge case - same time', () => {
      const currentDate = '15.08.2025';
      const currentTime = '20:20';
      
      // Рейд точно в текущее время - считается прошедшим
      expect(sheetsService.isRaidInFuture('15.08.2025', '20:20', currentDate, currentTime)).toBe(false);
    });

    test('should handle different date formats correctly', () => {
      // Текущее время: 15.08.2025 20:20
      const currentDate = '15.08.2025';
      const currentTime = '20:20';
      
      // Рейд в прошлом (вчера)
      expect(sheetsService.isRaidInFuture('13.08.2025', '23:00', currentDate, currentTime)).toBe(false);
      
      // Рейд в прошлом (сегодня, но раньше)
      expect(sheetsService.isRaidInFuture('15.08.2025', '15:00', currentDate, currentTime)).toBe(false);
      
      // Рейд в будущем (сегодня, но позже)
      expect(sheetsService.isRaidInFuture('15.08.2025', '22:00', currentDate, currentTime)).toBe(true);
      
      // Рейд в будущем (завтра)
      expect(sheetsService.isRaidInFuture('16.08.2025', '10:00', currentDate, currentTime)).toBe(true);
    });

    test('should filter out past raids with real data', async () => {
      // Мокаем метод getClientsData с реальными данными
      sheetsService.getClientsData = jest.fn().mockResolvedValue([
        {
          date: '13.08.2025',
          time: '23:00',
          raidType: 'MO Normal',
          accounts: 'Vasya',
          team: 'Team1'
        },
        {
          date: '14.08.2025',
          time: '10:00',
          raidType: 'MO Single Boss NM',
          accounts: 'Petya',
          team: 'Team2'
        },
        {
          date: '14.08.2025',
          time: '11:30',
          raidType: 'MO Single Boss HC',
          accounts: 'Kolya',
          team: 'Team3'
        },
        {
          date: '15.08.2025',
          time: '15:00',
          raidType: 'MO Normal',
          accounts: 'Sasha',
          team: 'Team4'
        },
        {
          date: '15.08.2025',
          time: '19:30',
          raidType: 'MO Single Boss HC',
          accounts: 'Misha',
          team: 'Team5'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Single Boss HC',
          accounts: 'Dima',
          team: 'Team6'
        },
        {
          date: '15.08.2025',
          time: '22:30',
          raidType: 'Dimensius Heroic',
          accounts: 'Artem',
          team: 'Team7'
        },
        {
          date: '15.08.2025',
          time: '23:30',
          raidType: 'MO Normal',
          accounts: 'Vlad',
          team: 'Team8'
        }
      ]);

      const raids = await sheetsService.getUpcomingRaidsWithAccounts();
      
      // Должны остаться только рейды после 20:25 (текущее время в тесте)
      expect(raids).toHaveLength(3);
      
      // Проверяем, что остались только будущие рейды
      const futureRaidTimes = raids.map(raid => `${raid.date} ${raid.time}`);
      expect(futureRaidTimes).toContain('15.08.2025 22:00');
      expect(futureRaidTimes).toContain('15.08.2025 22:30');
      expect(futureRaidTimes).toContain('15.08.2025 23:30');
      
      // Проверяем, что прошедшие рейды отфильтрованы
      expect(futureRaidTimes).not.toContain('13.08.2025 23:00');
      expect(futureRaidTimes).not.toContain('14.08.2025 10:00');
      expect(futureRaidTimes).not.toContain('14.08.2025 11:30');
      expect(futureRaidTimes).not.toContain('15.08.2025 15:00');
      expect(futureRaidTimes).not.toContain('15.08.2025 19:30');
    });
  });

  describe('Data Structure', () => {
    test('should return raids with correct properties', async () => {
      // Мокаем метод getClientsData для тестирования
      sheetsService.getClientsData = jest.fn().mockResolvedValue([
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Heroic h2s',
          accounts: 'Vasya',
          team: 'Team1'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Normal a2s',
          accounts: 'Petya',
          team: 'Team2'
        },
        {
          date: '16.08.2025',
          time: '11:00',
          raidType: 'MO Normal A1',
          accounts: 'Kolya',
          team: 'Team3'
        }
      ]);

      const raids = await sheetsService.getUpcomingRaidsWithAccounts();
      
      // Все рейды в будущем относительно текущего времени (15.08.2025 20:23)
      expect(raids).toHaveLength(3);
      expect(raids[0]).toHaveProperty('date');
      expect(raids[0]).toHaveProperty('time');
      expect(raids[0]).toHaveProperty('raidType');
      expect(raids[0]).toHaveProperty('clientCount');
      expect(raids[0]).toHaveProperty('teams');
    });

    test('should count clients with non-empty accounts field', async () => {
      sheetsService.getClientsData = jest.fn().mockResolvedValue([
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Heroic h2s',
          accounts: '',
          team: 'Team1'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Normal a2s',
          accounts: 'Petya',
          team: 'Team2'
        },
        {
          date: '16.08.2025',
          time: '11:00',
          raidType: 'MO Normal A1',
          accounts: 'Kolya',
          team: 'Team3'
        }
      ]);

      const raids = await sheetsService.getUpcomingRaidsWithAccounts();
      
      expect(raids).toHaveLength(2);
      expect(raids[0].raidType).toBe('MO Normal a2s');
      expect(raids[0].clientCount).toBe(1);
      expect(raids[1].raidType).toBe('MO Normal A1');
      expect(raids[1].clientCount).toBe(1);
    });

    test('should group multiple clients for same raid', async () => {
      sheetsService.getClientsData = jest.fn().mockResolvedValue([
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Heroic h2s',
          accounts: 'Vasya',
          team: 'Team1'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Heroic h2s',
          accounts: 'Petya',
          team: 'Team1'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Heroic h2s',
          accounts: 'Kolya',
          team: 'Team2'
        }
      ]);

      const raids = await sheetsService.getUpcomingRaidsWithAccounts();
      
      expect(raids).toHaveLength(1);
      expect(raids[0].raidType).toBe('MO Heroic h2s');
      expect(raids[0].clientCount).toBe(3);
    });

    test('should sort raids by date and time', async () => {
      sheetsService.getClientsData = jest.fn().mockResolvedValue([
        {
          date: '16.08.2025',
          time: '10:00',
          raidType: 'MO Heroic h2s',
          accounts: 'Vasya',
          team: 'Team1'
        },
        {
          date: '15.08.2025',
          time: '22:30',
          raidType: 'MO Normal A1',
          accounts: 'Kolya',
          team: 'Team3'
        },
        {
          date: '15.08.2025',
          time: '22:00',
          raidType: 'MO Normal a2s',
          accounts: 'Petya',
          team: 'Team2'
        }
      ]);

      const raids = await sheetsService.getUpcomingRaidsWithAccounts();
      
      expect(raids[0].date).toBe('15.08.2025');
      expect(raids[0].time).toBe('22:00');
      expect(raids[1].date).toBe('15.08.2025');
      expect(raids[1].time).toBe('22:30');
      expect(raids[2].date).toBe('16.08.2025');
      expect(raids[2].time).toBe('10:00');
    });
  });
});
