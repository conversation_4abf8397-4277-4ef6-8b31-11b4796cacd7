const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { config } = require('../config/config');

class SheetsService {
  constructor() {
    this.auth = null;
    this.sheets = null;
    this.initialized = false;
  }

  /**
   * Инициализация Google Sheets API
   */
  async initialize() {
    try {
      // Получаем ключи из переменной окружения
      if (!config.googleSheets.serviceAccountKey) {
        throw new Error('Не найден ключ Google Service Account в переменной GOOGLE_SERVICE_ACCOUNT_KEY');
      }

      // Парсим ключи сервисного аккаунта из JSON строки
      const credentials = JSON.parse(config.googleSheets.serviceAccountKey);

      // Создаем авторизацию
      this.auth = new google.auth.GoogleAuth({
        credentials,
        scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
      });

      // Инициализируем Sheets API
      this.sheets = google.sheets({ version: 'v4', auth: this.auth });
      this.initialized = true;

      console.log('Google Sheets API успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации Google Sheets API:', error.message);
      throw error;
    }
  }

  /**
   * Проверка инициализации
   */
  _checkInitialized() {
    if (!this.initialized) {
      throw new Error('SheetsService не инициализирован. Вызовите initialize() сначала.');
    }
  }

  /**
   * Получение данных из таблицы клиентов
   */
  async getClientsData() {
    this._checkInitialized();

    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: config.googleSheets.clientSpreadsheetId,
        range: config.googleSheets.clientsRange,
      });

      const rows = response.data.values || [];

      // Пропускаем заголовок (первую строку)
      const dataRows = rows.slice(1);

      const clients = dataRows.map((row, index) => {
        const rowNumber = index + 2; // +2 потому что пропустили заголовок и индексы начинаются с 0

        return {
          rowNumber,
          date: row[config.googleSheets.clientColumns.date] || '',
          orderId: row[config.googleSheets.clientColumns.orderId] || '',
          time: row[config.googleSheets.clientColumns.time] || '',
          team: row[config.googleSheets.clientColumns.team] || '',
          raidType: row[config.googleSheets.clientColumns.raidType] || '',
          accounts: row[config.googleSheets.clientColumns.accounts] || '',
          // Добавляем все данные строки для дополнительной обработки
          rawData: row,
        };
      }).filter(client =>
        // Фильтруем только строки с заполненными основными полями
        client.date && client.time && client.raidType
      );

      console.log(`Получено ${clients.length} записей клиентов`);
      return clients;
    } catch (error) {
      console.error('Ошибка получения данных клиентов:', error.message);
      throw error;
    }
  }

  /**
   * Получение данных из таблицы расписания
   */
  async getScheduleData() {
    this._checkInitialized();

    try {
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: config.googleSheets.scheduleSpreadsheetId,
        range: config.googleSheets.scheduleRange,
      });

      const rows = response.data.values || [];

      // Пропускаем заголовок (первую строку)
      const dataRows = rows.slice(1);

      const schedule = dataRows.map((row, index) => {
        const rowNumber = index + 2;

        return {
          rowNumber,
          date: row[config.googleSheets.scheduleColumns.date] || '',
          time: row[config.googleSheets.scheduleColumns.time] || '',
          raidType: row[config.googleSheets.scheduleColumns.raidType] || '',
          team: row[config.googleSheets.scheduleColumns.team] || '',
          status: row[config.googleSheets.scheduleColumns.status] || '',
          // Добавляем все данные строки
          rawData: row,
        };
      }).filter(item =>
        // Фильтруем только строки с заполненными основными полями
        item.date && item.time && item.raidType
      );

      console.log(`Получено ${schedule.length} записей расписания`);
      return schedule;
    } catch (error) {
      console.error('Ошибка получения данных расписания:', error.message);
      throw error;
    }
  }

  /**
   * Получение данных клиентов для конкретного рейда
   */
  async getClientsForRaid(date, time, raidType) {
    const clients = await this.getClientsData();

    return clients.filter(client =>
      client.date === date &&
      client.time === time &&
      client.raidType === raidType
    );
  }

  /**
   * Проверка статуса рейда в расписании (открыт/закрыт)
   */
  async isRaidOpen(date, time, raidType) {
    const schedule = await this.getScheduleData();

    const raidEntry = schedule.find(item =>
      item.date === date &&
      item.time === time &&
      item.raidType === raidType
    );

    if (!raidEntry) {
      return null; // Рейд не найден в расписании
    }

    // Считаем рейд открытым, если статус не содержит "закрыт" или "closed"
    const status = raidEntry.status.toLowerCase();
    return !status.includes('закрыт') && !status.includes('closed');
  }

  /**
   * Получение всех открытых рейдов из расписания
   */
  async getOpenRaids() {
    const schedule = await this.getScheduleData();

    return schedule.filter(item => {
      const status = item.status.toLowerCase();
      return !status.includes('закрыт') && !status.includes('closed');
    });
  }

  /**
   * Получение данных о количестве аккаунтов для рейдов через 30 минут
   */
  async getAccountsForUpcomingRaids(targetTime) {
    this._checkInitialized();

    try {
      const clients = await this.getClientsData();
      
      // Группируем клиентов по времени и типу рейда
      const raidsByTime = {};
      
      clients.forEach(client => {
        if (client.time === targetTime && client.accounts && client.accounts.trim() !== '') {
          const key = `${client.time}_${client.raidType}`;
          
          if (!raidsByTime[key]) {
            raidsByTime[key] = {
              time: client.time,
              raidType: client.raidType,
              totalAccounts: 0,
              teams: new Set()
            };
          }
          
          // Парсим количество аккаунтов (если это число)
          const accountsCount = parseInt(client.accounts) || 0;
          raidsByTime[key].totalAccounts += accountsCount;
          
          if (client.team) {
            raidsByTime[key].teams.add(client.team);
          }
        }
      });

      // Фильтруем только рейды с аккаунтами > 0
      const raidsWithAccounts = Object.values(raidsByTime)
        .filter(raid => raid.totalAccounts > 0);

      return raidsWithAccounts;
    } catch (error) {
      console.error('Ошибка получения данных об аккаунтах для предстоящих рейдов:', error.message);
      throw error;
    }
  }

  /**
   * Получение всех предстоящих рейдов с аккаунтами > 0
   */
  async getUpcomingRaidsWithAccounts() {
    this._checkInitialized();

    try {
      const clients = await this.getClientsData();
      
      // Получаем текущее время в CET
      const now = new Date();
      const cetTime = new Date(now.toLocaleString('en-US', { timeZone: 'Europe/Berlin' }));
      const currentDate = `${cetTime.getDate().toString().padStart(2, '0')}.${(cetTime.getMonth() + 1).toString().padStart(2, '0')}.${cetTime.getFullYear()}`;
      const currentTime = cetTime.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      
      console.log(`Текущее время CET: ${currentDate} ${currentTime}`);
      
      // Группируем клиентов по дате, времени и типу рейда
      const raidsByDateTime = {};
      
      clients.forEach(client => {
        if (client.accounts && client.accounts.trim() !== '') {
          // Проверяем, что рейд еще не начался
          if (this.isRaidInFuture(client.date, client.time, currentDate, currentTime)) {
            // Считаем количество клиентов с непустым столбцом W
            const key = `${client.date}_${client.time}_${client.raidType}`;
            
            if (!raidsByDateTime[key]) {
              raidsByDateTime[key] = {
                date: client.date,
                time: client.time,
                raidType: client.raidType,
                clientCount: 0,
                teams: new Set()
              };
            }
            
            // Увеличиваем счетчик клиентов
            raidsByDateTime[key].clientCount += 1;
            if (client.team) {
              raidsByDateTime[key].teams.add(client.team);
            }
          }
        }
      });

      // Фильтруем только рейды с клиентами > 0 и сортируем по дате и времени
      const raidsWithAccounts = Object.values(raidsByDateTime)
        .filter(raid => raid.clientCount > 0)
        .sort((a, b) => {
          // Сначала по дате
          const dateComparison = a.date.localeCompare(b.date);
          if (dateComparison !== 0) return dateComparison;
          // Затем по времени
          return a.time.localeCompare(b.time);
        });

      return raidsWithAccounts;
    } catch (error) {
      console.error('Ошибка получения данных о предстоящих рейдах с аккаунтами:', error.message);
      throw error;
    }
  }

  /**
   * Проверка, что рейд еще не начался
   */
  isRaidInFuture(raidDate, raidTime, currentDate, currentTime) {
    // Парсим даты в объекты Date для корректного сравнения
    const parseDate = (dateStr) => {
      const parts = dateStr.split('.');
      if (parts.length === 3) {
        // Формат DD.MM.YYYY
        const [day, month, year] = parts;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }
      return null;
    };

    const raidDateObj = parseDate(raidDate);
    const currentDateObj = parseDate(currentDate);

    if (!raidDateObj || !currentDateObj) {
      console.warn(`Не удалось распарсить дату: raidDate=${raidDate}, currentDate=${currentDate}`);
      return false;
    }

    // Сравниваем даты
    if (raidDateObj.getTime() !== currentDateObj.getTime()) {
      return raidDateObj > currentDateObj;
    }
    
    // Если дата одинаковая, сравниваем время
    const [raidHour, raidMinute] = raidTime.split(':').map(Number);
    const [currentHour, currentMinute] = currentTime.split(':').map(Number);
    
    if (raidHour !== currentHour) {
      return raidHour > currentHour;
    }
    return raidMinute > currentMinute;
  }
}

module.exports = SheetsService;