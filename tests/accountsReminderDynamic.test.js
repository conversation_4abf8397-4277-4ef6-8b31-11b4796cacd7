const RaidMonitor = require('../src/services/raidMonitor');
const { config } = require('../src/config/config');

// Мокаем cron
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn()
  }))
}));

const cron = require('node-cron');

describe('Accounts Reminder Dynamic Cron Tests', () => {
  let raidMonitor;

  beforeEach(() => {
    jest.clearAllMocks();
    
    raidMonitor = new RaidMonitor();
    
    // Мокаем зависимости
    raidMonitor.sheetsService = {
      getClientsData: jest.fn()
    };
    
    raidMonitor.scheduleService = {
      getCombinedScheduleData: jest.fn()
    };
    
    raidMonitor.notificationService = {
      sendNotification: jest.fn()
    };
    
    raidMonitor.notifiedStore = {
      isNotified: jest.fn().mockReturnValue(false),
      markAsNotified: jest.fn()
    };
    
    raidMonitor.dynamicConfigService = {
      getAllowedRaidTypes: jest.fn().mockResolvedValue(config.allowedRaidTypes)
    };
    
    raidMonitor.internalScheduleService = {
      getInternalScheduleData: jest.fn().mockResolvedValue([])
    };
    
    raidMonitor.dynamicCronJobs = new Map();
    raidMonitor.initialized = true;
  });

  describe('setupDynamicAccountsChecks', () => {
    test('should create dynamic cron jobs for accounts checks', async () => {
      const futureRaids = [
        {
          date: '16.08.2025',
          time: '21:00',
          raidType: 'MO Heroic',
          status: 'открыт'
        },
        {
          date: '16.08.2025',
          time: '22:00',
          raidType: 'LoU Normal',
          status: 'открыт'
        }
      ];

      const now = new Date('2025-08-16T18:00:00Z'); // 20:00 CET

      await raidMonitor.setupDynamicAccountsChecks(futureRaids, now);

      // Проверяем, что cron.schedule был вызван для каждого рейда
      expect(cron.schedule).toHaveBeenCalledTimes(2);
      
      // Проверяем, что задачи добавлены в Map
      expect(raidMonitor.dynamicCronJobs.size).toBe(2);
      
      // Проверяем ключи задач
      const jobKeys = Array.from(raidMonitor.dynamicCronJobs.keys());
      expect(jobKeys).toContain('accounts_16.08.2025|21:00|MO Heroic');
      expect(jobKeys).toContain('accounts_16.08.2025|22:00|LoU Normal');
    });

    test('should skip raids where accounts check time has passed', async () => {
      const futureRaids = [
        {
          date: '16.08.2025',
          time: '18:15', // Через 15 минут, но проверка аккаунтов должна быть за 30 минут (уже прошла)
          raidType: 'MO Heroic',
          status: 'открыт'
        }
      ];

      const now = new Date('2025-08-16T16:00:00Z'); // 18:00 CET

      await raidMonitor.setupDynamicAccountsChecks(futureRaids, now);

      // Не должно создаваться задач, так как время проверки уже прошло
      expect(cron.schedule).not.toHaveBeenCalled();
      expect(raidMonitor.dynamicCronJobs.size).toBe(0);
    });
  });

  describe('performAccountsCheckForRaid', () => {
    test('should send notification for raid with accounts', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      const clientsWithAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: 'Player1',
          team: 'h2s'
        },
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: 'Player2',
          team: 'h2s'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithAccounts);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление было отправлено
      expect(raidMonitor.notificationService.sendNotification).toHaveBeenCalledWith(
        expect.stringContaining('🎮 Через 30 минут рейд/рейды, не забудьте пошарить 2 аккаунтов')
      );
      
      // Проверяем, что уведомление помечено как отправленное
      expect(raidMonitor.notifiedStore.markAsNotified).toHaveBeenCalledWith('accounts_16.08.2025_20:00');
    });

    test('should not send notification if no clients with accounts', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      const clientsWithoutAccounts = [
        {
          date: '16.08.2025',
          time: '20:00',
          raidType: 'MO Heroic',
          accounts: '', // Пустое поле аккаунтов
          team: 'h2s'
        }
      ];

      raidMonitor.sheetsService.getClientsData.mockResolvedValue(clientsWithoutAccounts);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление НЕ было отправлено
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
      expect(raidMonitor.notifiedStore.markAsNotified).not.toHaveBeenCalled();
    });

    test('should not send duplicate notifications', async () => {
      const raid = {
        date: '16.08.2025',
        time: '20:00',
        raidType: 'MO Heroic'
      };

      // Мокаем, что уведомление уже было отправлено
      raidMonitor.notifiedStore.isNotified.mockReturnValue(true);

      await raidMonitor.performAccountsCheckForRaid(raid);

      // Проверяем, что уведомление НЕ было отправлено повторно
      expect(raidMonitor.notificationService.sendNotification).not.toHaveBeenCalled();
    });
  });
});
