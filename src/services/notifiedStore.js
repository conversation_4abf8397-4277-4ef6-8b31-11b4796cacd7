const fs = require('fs');
const path = require('path');

class NotifiedStore {
  constructor() {
    this.filePath = path.join(__dirname, '..', 'data', 'notified.json');
    this.data = {
      pastDate: [],
      pastTime: [],
      seenPastDate: {},
      seenPastTime: {},
    };
    this.sets = {
      pastDate: new Set(),
      pastTime: new Set(),
    };
  }

  async initialize() {
    try {
      // Ensure directory exists
      const dir = path.dirname(this.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      if (fs.existsSync(this.filePath)) {
        const raw = fs.readFileSync(this.filePath, 'utf8');
        const parsed = JSON.parse(raw || '{}');
        this.data.pastDate = Array.isArray(parsed.pastDate) ? parsed.pastDate : [];
        this.data.pastTime = Array.isArray(parsed.pastTime) ? parsed.pastTime : [];
        this.data.seenPastDate = parsed.seenPastDate && typeof parsed.seenPastDate === 'object' ? parsed.seenPastDate : {};
        this.data.seenPastTime = parsed.seenPastTime && typeof parsed.seenPastTime === 'object' ? parsed.seenPastTime : {};
      }

      // Hydrate sets
      this.sets.pastDate = new Set(this.data.pastDate);
      this.sets.pastTime = new Set(this.data.pastTime);
    } catch (e) {
      // Start fresh on error
      this.data = { pastDate: [], pastTime: [], seenPastDate: {}, seenPastTime: {} };
      this.sets = { pastDate: new Set(), pastTime: new Set() };
    }
  }

  save() {
    this.data.pastDate = Array.from(this.sets.pastDate);
    this.data.pastTime = Array.from(this.sets.pastTime);
    fs.writeFileSync(this.filePath, JSON.stringify(this.data, null, 2), 'utf8');
  }

  has(category, key) {
    const set = this.sets[category];
    return !!(set && set.has(key));
  }

  add(category, key) {
    if (!this.sets[category]) {
      this.sets[category] = new Set();
    }
    this.sets[category].add(key);
    this.save();
  }

  bulkAdd(category, keys) {
    if (!this.sets[category]) {
      this.sets[category] = new Set();
    }
    for (const k of keys) {
      this.sets[category].add(k);
    }
    this.save();
  }

  // Seen signatures (to detect content changes vs. maturation by time)
  getSeen(category, key) {
    if (category === 'pastDate') return this.data.seenPastDate?.[key];
    if (category === 'pastTime') return this.data.seenPastTime?.[key];
    return undefined;
  }

  setSeen(category, key, signature) {
    if (category === 'pastDate') {
      this.data.seenPastDate[key] = signature;
    } else if (category === 'pastTime') {
      this.data.seenPastTime[key] = signature;
    }
    this.save();
  }

  bulkSetSeen(category, entriesObject) {
    if (category === 'pastDate') {
      this.data.seenPastDate = { ...(this.data.seenPastDate || {}), ...entriesObject };
    } else if (category === 'pastTime') {
      this.data.seenPastTime = { ...(this.data.seenPastTime || {}), ...entriesObject };
    }
    this.save();
  }
}

module.exports = NotifiedStore;


