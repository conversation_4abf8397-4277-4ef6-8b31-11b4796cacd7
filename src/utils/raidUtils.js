const { config } = require('../config/config');
const DateUtils = require('./dateUtils');

/**
 * Утилиты для работы с рейдами
 */
class RaidUtils {
  /**
   * Проверка, является ли рейд мифическим
   */
  static isMythicRaid(raidType) {
    if (!raidType) return false;
    return raidType.toLowerCase().includes('mythic');
  }

  /**
   * Группировка клиентов по составу, дате и времени
   */
  static groupClientsByRaid(clients) {
    const groups = {};

    for (const client of clients) {
      // Создаем ключ для группировки: дата + время + тип рейда
      const key = `${client.date}|${client.time}|${client.raidType}`;

      if (!groups[key]) {
        groups[key] = {
          date: client.date,
          time: client.time,
          raidType: client.raidType,
          clients: [],
          teams: new Set(),
        };
      }

      groups[key].clients.push(client);

      // Добавляем команду в множество (если указана)
      if (client.team && client.team.trim()) {
        groups[key].teams.add(client.team.trim());
      }
    }

    // Преобразуем множества команд в массивы
    Object.values(groups).forEach(group => {
      group.teams = Array.from(group.teams);
      group.clientCount = group.clients.length;
    });

    return groups;
  }

  /**
   * Проверка соответствия типа рейда допустимым типам
   */
  static isRaidTypeAllowed(scheduleRaidType, clientRaidType, allowedRaidTypes = null) {
    // Используем переданную конфигурацию или статическую
    const allowedTypes = allowedRaidTypes ? allowedRaidTypes[scheduleRaidType] : config.allowedRaidTypes[scheduleRaidType];

    // Проверяем точное совпадение
    if (scheduleRaidType === clientRaidType) {
      return true;
    }

    // Проверяем список допустимых типов
    if (allowedTypes && allowedTypes.includes(clientRaidType)) {
      return true;
    }

    // Гибкое сопоставление для Mythic рейдов
    const scheduleIsMythic = scheduleRaidType.toLowerCase().includes('mythic');
    const clientIsMythic = clientRaidType.toLowerCase().includes('mythic');

    if (scheduleIsMythic && clientIsMythic) {
      return true;
    }

    // Если нет настроенных допустимых типов, используем только точное совпадение
    if (!allowedTypes) {
      return false;
    }

    return false;
  }

  /**
   * Получение всех недопустимых типов рейдов для клиентов
   */
  static findRaidTypeMismatches(scheduleRaids, clientGroups, allowedRaidTypes = null, ignoredOrderIds = null) {
    const mismatches = [];

    // Игнор-лист типов рейдов, которые не нужно проверять
    const ignoredRaidTypes = ['Key', 'KeyH', 'Additional payment'];

    // Текущее время для фильтрации старых рейдов
    const now = new Date();
    const threeHoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 часа назад

    // Проверяем каждую группу клиентов
    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');

      // Игнорируем определенные типы рейдов
      if (ignoredRaidTypes.includes(clientRaidType)) {
        continue;
      }

      // Проверяем, что рейд не старше 3 часов
      const raidDateTime = DateUtils.createDateTime(clientDate, clientTime);
      if (!raidDateTime || !raidDateTime.isValid() || raidDateTime.toDate() < threeHoursAgo) {
        continue; // Пропускаем старые рейды
      }

      // Ищем все рейды в расписании с такой же датой и временем
      const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
        scheduleRaid.date === clientDate && scheduleRaid.time === clientTime
      );

      if (matchingScheduleRaids.length === 0) {
        // Если в расписании нет рейдов на это время, это не ошибка типа
        continue;
      }

      // Проверяем, подходит ли тип клиентов хотя бы к одному рейду в расписании
      const hasMatchingRaid = matchingScheduleRaids.some(scheduleRaid =>
        this.isRaidTypeAllowed(scheduleRaid.raidType, clientRaidType, allowedRaidTypes)
      );

      if (!hasMatchingRaid) {
        // Если ни один рейд в расписании не подходит, это несоответствие
        const scheduleRaidTypes = matchingScheduleRaids.map(r => r.raidType).join(', ');

        // Фильтруем клиентов по игнор-листу
        const filteredClients = ignoredOrderIds
          ? clientGroup.clients.filter(c => !(c.orderId && ignoredOrderIds.has(c.orderId)))
          : clientGroup.clients;

        if (filteredClients.length === 0) {
          continue;
        }

        mismatches.push({
          scheduleRaidType: scheduleRaidTypes,
          clientRaidType: clientRaidType,
          date: clientDate,
          time: clientTime,
          clientCount: filteredClients.length,
          clientsDetailed: filteredClients.map(client => ({
            rowNumber: client.rowNumber,
            orderId: client.orderId,
            clientRaidType: client.raidType,
          })),
        });
      }
    }

    return mismatches;
  }

  /**
   * Форсированная проверка типов рейдов (для команды /teams)
   * Игнорирует только старые рейды (>3 часов), но сохраняет игнор-лист типов
   */
  static findRaidTypeMismatchesForced(scheduleRaids, clientGroups, allowedRaidTypes = null, ignoredOrderIds = null) {
    const mismatches = [];

    // Игнор-лист типов рейдов, которые не нужно проверять
    const ignoredRaidTypes = ['Key', 'KeyH', 'Additional payment'];

    // Текущее время для фильтрации старых рейдов
    const now = new Date();
    const threeHoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 часа назад

    // Проверяем каждую группу клиентов
    for (const [clientKey, clientGroup] of Object.entries(clientGroups)) {
      const [clientDate, clientTime, clientRaidType] = clientKey.split('|');

      // Игнорируем определенные типы рейдов
      if (ignoredRaidTypes.includes(clientRaidType)) {
        continue;
      }

      // Проверяем, что рейд не старше 3 часов
      const raidDateTime = DateUtils.createDateTime(clientDate, clientTime);
      if (!raidDateTime || !raidDateTime.isValid() || raidDateTime.toDate() < threeHoursAgo) {
        continue; // Пропускаем старые рейды
      }

      // Ищем все рейды в расписании с такой же датой и временем
      const matchingScheduleRaids = scheduleRaids.filter(scheduleRaid =>
        scheduleRaid.date === clientDate && scheduleRaid.time === clientTime
      );

      if (matchingScheduleRaids.length === 0) {
        // Если в расписании нет рейдов на это время, это не ошибка типа
        continue;
      }

      // Проверяем, подходит ли тип клиентов хотя бы к одному рейду в расписании
      const hasMatchingRaid = matchingScheduleRaids.some(scheduleRaid =>
        this.isRaidTypeAllowed(scheduleRaid.raidType, clientRaidType, allowedRaidTypes)
      );

      if (!hasMatchingRaid) {
        // Если ни один рейд в расписании не подходит, это несоответствие
        const scheduleRaidTypes = matchingScheduleRaids.map(r => r.raidType).join(', ');

        // Фильтруем клиентов по игнор-листу
        const filteredClients = ignoredOrderIds
          ? clientGroup.clients.filter(c => !(c.orderId && ignoredOrderIds.has(c.orderId)))
          : clientGroup.clients;

        if (filteredClients.length === 0) {
          continue;
        }

        mismatches.push({
          scheduleRaidType: scheduleRaidTypes,
          clientRaidType: clientRaidType,
          date: clientDate,
          time: clientTime,
          clientCount: filteredClients.length,
          clientsDetailed: filteredClients.map(client => ({
            rowNumber: client.rowNumber,
            orderId: client.orderId,
            clientRaidType: client.raidType,
          })),
        });
      }
    }

    return mismatches;
  }

  /**
   * Создание объекта Date из строки даты и времени
   */
  static createDateTime(dateStr, timeStr) {
    try {
      const [day, month, year] = dateStr.split('.');
      const [hours, minutes] = timeStr.split(':');

      return new Date(
        parseInt(year),
        parseInt(month) - 1, // месяцы в JS начинаются с 0
        parseInt(day),
        parseInt(hours),
        parseInt(minutes)
      );
    } catch (error) {
      console.error(`Ошибка парсинга даты/времени: ${dateStr} ${timeStr}`, error);
      return null;
    }
  }

  /**
   * Проверка команд для рейда (упрощенная - только на пустое значение)
   */
  static checkTeamAssignments(scheduleRaid, clients, ignoredOrderIds = null) {
    const issues = [];

    // scheduleRaid не используется в упрощенной проверке, но оставляем для совместимости API
    for (const client of clients) {
      // Игнорируем клиентов из игнор-листа по заказу
      if (ignoredOrderIds && client.orderId && ignoredOrderIds.has(client.orderId)) {
        continue;
      }
      if (!client.team || client.team.trim() === '') {
        const orderInfo = client.orderId ? `Заказ: "${client.orderId}" ` : '';
        issues.push(`${orderInfo}Клиент без назначенной команды (строка ${client.rowNumber})`);
      }
      // Убираем проверку на соответствие конкретной команде, так как в расписании нет информации о составе
    }

    return issues;
  }

  /**
   * Поиск новых мифических рейдов
   */
  static findNewMythicRaids(currentClients, previousClients = []) {
    const currentMythicRaids = new Set();
    const previousMythicRaids = new Set();

    // Собираем текущие мифические рейды
    for (const client of currentClients) {
      if (this.isMythicRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        currentMythicRaids.add(key);
      }
    }

    // Собираем предыдущие мифические рейды
    for (const client of previousClients) {
      if (this.isMythicRaid(client.raidType)) {
        const key = `${client.date}|${client.time}|${client.raidType}`;
        previousMythicRaids.add(key);
      }
    }

    // Находим новые рейды (есть в текущих, но нет в предыдущих)
    const newRaids = [];
    for (const raidKey of currentMythicRaids) {
      if (!previousMythicRaids.has(raidKey)) {
        const [date, time, raidType] = raidKey.split('|');
        newRaids.push({ date, time, raidType });
      }
    }

    return newRaids;
  }

  /**
   * Нормализация названия команды
   */
  static normalizeTeamName(teamName) {
    if (!teamName) return '';
    return teamName.trim().toLowerCase();
  }

  /**
   * Проверка, заполнен ли рейд (достиг ли порогового количества клиентов)
   */
  static isRaidFull(clientCount, threshold = null) {
    const minThreshold = threshold || config.monitoring.minClientsThreshold;
    return clientCount >= minThreshold;
  }

  /**
   * Получение краткого описания рейда
   */
  static getRaidDescription(date, time, raidType) {
    return `${raidType} ${date} ${time}`;
  }

  /**
   * Сортировка рейдов по дате и времени
   */
  static sortRaidsByDateTime(raids) {
    return raids.sort((a, b) => {
      // Сначала по дате
      const dateComparison = a.date.localeCompare(b.date);
      if (dateComparison !== 0) return dateComparison;

      // Затем по времени
      return a.time.localeCompare(b.time);
    });
  }

  /**
   * Получение лимита рейда по умолчанию (без mythic)
   */
  static getDefaultRaidLimit(raidType) {
    const raidTypeLower = raidType.toLowerCase();

    if (raidTypeLower.includes('heroic')) {
      return config.raidCapacityLimits.heroic || 25;
    } else if (raidTypeLower.includes('normal')) {
      return config.raidCapacityLimits.normal || 30;
    }

    return config.raidCapacityLimits.default || 20;
  }
}

module.exports = RaidUtils;