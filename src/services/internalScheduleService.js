const { google } = require('googleapis');
const { config } = require('../config/config');

/**
 * Сервис для работы с внутренним расписанием (составы команд)
 */
class InternalScheduleService {
  constructor() {
    this.sheets = null;
    this.initialized = false;
  }

  /**
   * Инициализация Google Sheets API
   */
  async initialize() {
    try {
      console.log('Инициализация InternalScheduleService...');

      // Парсим ключ сервисного аккаунта
      const serviceAccountKey = JSON.parse(config.googleSheets.serviceAccountKey);

      // Создаем JWT клиент
      const auth = new google.auth.JWT(
        serviceAccountKey.client_email,
        null,
        serviceAccountKey.private_key,
        ['https://www.googleapis.com/auth/spreadsheets.readonly']
      );

      // Инициализируем Sheets API
      this.sheets = google.sheets({ version: 'v4', auth });

      this.initialized = true;
      console.log('InternalScheduleService успешно инициализирован');
    } catch (error) {
      console.error('Ошибка инициализации InternalScheduleService:', error.message);
      throw error;
    }
  }

  /**
   * Получение данных внутреннего расписания из Google Sheets
   */
  async getInternalScheduleData() {
    if (!this.initialized) {
      throw new Error('InternalScheduleService не инициализирован');
    }

    try {
      console.log('Получение данных внутреннего расписания из Google Sheets...');

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: config.googleSheets.scheduleSpreadsheetId,
        range: `${config.googleSheets.internalScheduleSheetName}!${config.googleSheets.internalScheduleRange}`,
      });

      const rows = response.data.values || [];
      console.log(`Получено ${rows.length} строк из внутреннего расписания`);

      return this.parseInternalScheduleData(rows);
    } catch (error) {
      console.error('Ошибка получения данных внутреннего расписания:', error.message);
      throw error;
    }
  }

  /**
   * Парсинг данных внутреннего расписания из строк таблицы
   */
  parseInternalScheduleData(rows) {
    const schedule = [];
    let currentDate = null;
    let currentDayName = null;

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];

      // Пропускаем пустые строки
      if (!row || row.length === 0) {
        continue;
      }

      // Проверяем, является ли строка заголовком дня недели
      if (this.isDayHeader(row, i)) {
        currentDayName = row[1]; // День недели в столбце O (индекс 1)

        // Дата находится в столбце P этой же строки (индекс 2)
        if (row[2]) {
          currentDate = this.parseDate(row[2]);
        } else {
          // Попробуем найти дату в следующей строке
          if (i + 1 < rows.length && rows[i + 1] && rows[i + 1][2]) {
            currentDate = this.parseDate(rows[i + 1][2]);
          }
        }
        continue;
      }

      // Если у нас есть текущая дата и это строка с рейдом
      if (currentDate && row[0] && row[1] && row[2]) {
        const team = row[0].trim().toLowerCase();
        const time = this.parseTime(row[1]);
        const raidType = row[2].trim();

        if (team && time && raidType) {
          schedule.push({
            date: this.formatDateForDisplay(currentDate),
            time: time,
            raidType: raidType,
            team: team,
            dayName: currentDayName
          });
        }
      }
    }

    console.log(`Обработано ${schedule.length} записей внутреннего расписания`);
    return schedule;
  }

  /**
   * Проверка, является ли строка заголовком дня недели
   */
  isDayHeader(row, rowIndex) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    // Проверяем столбец O (индекс 1) на наличие дня недели
    const cellValue = row[1] ? row[1].toLowerCase().trim() : '';

    return dayNames.some(day => cellValue.includes(day));
  }

  /**
   * Парсинг даты из строки или объекта Date
   */
  parseDate(dateValue) {
    if (!dateValue) return null;

    try {
      // Если это уже объект Date
      if (dateValue instanceof Date) {
        return dateValue;
      }

      // Если это строка, пытаемся распарсить
      if (typeof dateValue === 'string') {
        // Сначала пробуем европейский формат DD.MM.YYYY
        const europeanMatch = dateValue.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
        if (europeanMatch) {
          const [, day, month, year] = europeanMatch;
          const date = new Date(year, month - 1, day);

          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Если не получилось, пробуем стандартный парсинг
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Если это число (timestamp)
      if (typeof dateValue === 'number') {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      return null;
    } catch (error) {
      console.error('Ошибка парсинга даты:', dateValue, error.message);
      return null;
    }
  }

  /**
   * Парсинг времени из строки
   */
  parseTime(timeValue) {
    if (!timeValue) return null;

    try {
      const timeStr = timeValue.toString().trim();

      // Ищем паттерн HH:MM
      const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})/);
      if (timeMatch) {
        const hours = timeMatch[1].padStart(2, '0');
        const minutes = timeMatch[2];
        return `${hours}:${minutes}`;
      }

      return null;
    } catch (error) {
      console.error('Ошибка парсинга времени:', timeValue, error.message);
      return null;
    }
  }

  /**
   * Форматирование даты для отображения (DD.MM.YYYY)
   */
  formatDateForDisplay(date) {
    if (!date) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
  }

  /**
   * Получение лимитов для команд на определенную дату и время
   */
  getTeamLimitsForRaid(date, time, raidType, internalRaids = null) {
    // Если данные не переданы, получаем их
    if (!internalRaids) {
      throw new Error('Данные внутреннего расписания должны быть переданы в метод');
    }

    // Находим все команды для этого рейда
    const teamsForRaid = internalRaids.filter(raid =>
      raid.date === date &&
      raid.time === time &&
      raid.raidType === raidType
    );

    // Определяем сложность рейда
    const difficulty = this.getRaidDifficulty(raidType);

    // Суммируем лимиты всех команд
    let totalLimit = 0;
    const teamDetails = [];

    teamsForRaid.forEach(raid => {
      const teamLimits = config.teamCapacityLimits[raid.team];
      if (teamLimits && teamLimits[difficulty]) {
        const limit = teamLimits[difficulty];
        totalLimit += limit;
        teamDetails.push({
          team: raid.team,
          limit: limit
        });
      }
    });

    return {
      totalLimit,
      teamDetails,
      difficulty
    };
  }

  /**
   * Определение сложности рейда по названию
   */
  getRaidDifficulty(raidType) {
    const raidTypeLower = raidType.toLowerCase();

    if (raidTypeLower.includes('mythic')) {
      return 'mythic';
    } else if (raidTypeLower.includes('heroic')) {
      return 'heroic';
    } else if (raidTypeLower.includes('normal')) {
      return 'normal';
    }

    // По умолчанию считаем normal
    return 'normal';
  }
}

module.exports = InternalScheduleService;