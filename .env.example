# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Google Sheets Configuration
GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID=your_client_spreadsheet_id_here
GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID=your_schedule_spreadsheet_id_here
GOOGLE_SERVICE_ACCOUNT_KEY=your_google_service_account_key_json_here

# Monitoring Configuration (optional - can be configured via web interface)
CHECK_INTERVAL_MINUTES=10
MYTHIC_NOTIFICATION_DELAY_MINUTES=15
PRE_RAID_CHECK_MINUTES=10
MIN_CLIENTS_THRESHOLD=20

# Timezone
TIMEZONE=Europe/Berlin

# Logging
LOG_LEVEL=info

# Web Interface Configuration
ADMIN_PASSWORD=admin123
SESSION_SECRET=wow-raid-bot-secret-key
MODE=web

# For Vercel deployment
# Set MODE=web in Vercel environment variables
