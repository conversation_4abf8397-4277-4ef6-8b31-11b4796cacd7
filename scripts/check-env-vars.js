#!/usr/bin/env node

/**
 * Скрипт для проверки переменных окружения
 */

require('dotenv').config();

function checkEnvVars() {
  console.log('🔍 Проверка переменных окружения...\n');

  const requiredVars = [
    'TELEGRAM_BOT_TOKEN',
    'TELEGRAM_CHAT_ID',
    'GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID',
    'GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID',
    'GOOGLE_SERVICE_ACCOUNT_KEY',
    'ADMIN_PASSWORD',
    'ADMIN_PASSWORD'
  ];

  let allGood = true;

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    
    if (!value) {
      console.log(`❌ ${varName}: НЕ УСТАНОВЛЕНА`);
      allGood = false;
    } else {
      console.log(`✅ ${varName}: установлена (${value.length} символов)`);
      
      // Специальная проверка для Google Service Account Key
      if (varName === 'GOOGLE_SERVICE_ACCOUNT_KEY') {
        try {
          let serviceAccountKey;
          
          // Пробуем парсить как есть
          try {
            serviceAccountKey = JSON.parse(value);
          } catch (firstError) {
            // Пробуем очистить
            let cleanedKey = value;
            
            if (cleanedKey.startsWith('"') && cleanedKey.endsWith('"')) {
              cleanedKey = cleanedKey.slice(1, -1);
            }
            
            cleanedKey = cleanedKey.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            serviceAccountKey = JSON.parse(cleanedKey);
          }
          
          console.log(`   📧 Client Email: ${serviceAccountKey.client_email}`);
          console.log(`   🆔 Project ID: ${serviceAccountKey.project_id}`);
          console.log(`   🔑 Private Key: ${serviceAccountKey.private_key ? 'присутствует' : 'отсутствует'}`);
          
        } catch (error) {
          console.log(`   ❌ Ошибка парсинга JSON: ${error.message}`);
          console.log(`   📝 Первые 100 символов: ${value.substring(0, 100)}...`);
          allGood = false;
        }
      }
    }
  });

  console.log('\n📊 Результат проверки:');
  if (allGood) {
    console.log('✅ Все переменные окружения настроены корректно!');
  } else {
    console.log('❌ Некоторые переменные отсутствуют или некорректны');
    console.log('💡 Проверьте файл .env');
  }

  return allGood;
}

// Запуск скрипта
if (require.main === module) {
  const success = checkEnvVars();
  process.exit(success ? 0 : 1);
}

module.exports = { checkEnvVars };
