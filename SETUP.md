# Быстрая настройка WoW Raid Reminder Bot

## 1. Создание Telegram бота

1. Найдите @BotFather в Telegram
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Сохраните полученный токен

## 2. Получение Chat ID

1. Добавьте бота в нужную группу/канал
2. Отправьте любое сообщение в группу
3. Перейдите по ссылке: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
4. Найдите `chat.id` в ответе

## 3. Настройка Google Sheets API

1. Перейдите в [Google Cloud Console](https://console.cloud.google.com/)
2. Создайте новый проект или выберите существующий
3. Включите Google Sheets API
4. Создайте Service Account:
   - Перейдите в "IAM & Admin" > "Service Accounts"
   - Нажмите "Create Service Account"
   - Заполните данные и создайте
5. Создайте ключ для Service Account:
   - Выберите созданный аккаунт
   - Перейдите в "Keys" > "Add Key" > "Create New Key"
   - Выберите JSON формат
   - Скачайте файл

## 4. Настройка доступа к таблицам

1. Откройте ваши Google Sheets
2. Нажмите "Share" (Поделиться)
3. Добавьте email вашего Service Account с правами "Viewer"
4. Скопируйте ID таблиц из URL (часть между `/d/` и `/edit`)

## 5. Настройка проекта

1. Скопируйте содержимое JSON файла ключей
2. Добавьте его в переменную `GOOGLE_SERVICE_ACCOUNT_KEY` в файле `.env`:

```env
TELEGRAM_BOT_TOKEN=**********:ABCDEFGHIJKLMNOPQRSTUVWXYZ
TELEGRAM_CHAT_ID=-*************
GOOGLE_SHEETS_CLIENT_SPREADSHEET_ID=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
GOOGLE_SHEETS_SCHEDULE_SPREADSHEET_ID=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project",...}
CHECK_INTERVAL_MINUTES=10
MYTHIC_NOTIFICATION_DELAY_MINUTES=15
PRE_RAID_CHECK_MINUTES=10
ACCOUNTS_REMINDER_MINUTES=30
MIN_CLIENTS_THRESHOLD=20
TIMEZONE=Europe/Moscow
```

## 6. Запуск

```bash
# Установка зависимостей (если еще не установлены)
npm install

# Запуск в режиме разработки
npm run dev

# Или запуск в продакшн режиме
npm start
```

## 7. Проверка работы

1. Отправьте боту команду `/start`
2. Используйте `/test` для проверки отправки сообщений
3. Проверьте `/status` для просмотра состояния бота

## Доступные команды

- `/start` - Приветствие и основная информация
- `/status` - Статус бота
- `/test` - Тестовое сообщение
- `/stats` - Статистика мониторинга
- `/filled` - Таблица заполненности рейдов
- `/teams` - Проверка составов команд
- `/piloted` - Список рейдов с аккаунтами
- `/help` - Помощь
- `/ignore <orderId>` - Игнорировать ошибки по заказу

## Структура данных

### Таблица клиентов (Google Sheets)
- **Столбец B**: Дата (28.07.2025)
- **Столбец T**: Время (20:30)
- **Столбец U**: Команда (Team Alpha)
- **Столбец V**: Тип рейда (LoU Heroic)
- **Столбец W**: Никнейм исполнителя (для напоминаний за 30 минут до рейда)

### Расписание рейдов (Google Sheets)
Получается из отдельной Google Sheets таблицы.

**ID таблицы**: `1dzPdrJGulpXPlTEhsoiXtGQCwGQJo8d60Wlv4igBQOM`
**Лист**: `Import`

**Структура**:
- Заголовки дней недели (Понедельник, Вторник, и т.д.) в столбце A
- Дата дня в столбце D заголовка (формат DD.MM.YYYY)
- Время рейдов в столбце A (19:00 CET)
- Названия рейдов в столбце B (LoU Heroic)
- Статус в столбце D (TRUE = закрыт, FALSE/пустое = открыт)

**Примечание**: Чекбокс TRUE означает что рейд закрыт.

## Новые функции

### Напоминания об аккаунтах
Бот автоматически проверяет каждые 5 минут количество клиентов с исполнителями в столбце W для рейдов, которые начнутся через 30 минут. Если в столбце W есть заполненные ячейки с никнеймами исполнителей, бот отправляет уведомление:

```
🎮 Через 30 минут рейд/рейды, не забудьте пошарить X аккаунтов
⏰ HH:MM
```

**Настройка**: `ACCOUNTS_REMINDER_MINUTES=30` (по умолчанию 30 минут)

### Команда /piloted
Бот поддерживает команду `/piloted`, которая выводит список всех **предстоящих** рейдов с исполнителями в столбце W в формате:

```
🎮 Ближайшие рейды с шарами:
⏰ Текущее время CET: 15.08.2025 20:20

15.08.2025 22:00 MO Heroic h2s - 2
15.08.2025 22:30 MO Normal a2s - 1
16.08.2025 11:00 MO Normal A1 - 5
```

**Особенности:**
- Показывает только рейды, которые еще не начались
- Автоматически группирует рейды по дате и времени
- Подсчитывает количество клиентов с непустым столбцом W (никнеймы исполнителей)
- Сортирует по дате и времени
- Отображает текущее время CET в начале сообщения

## Возможные проблемы

1. **Ошибка доступа к Google Sheets**: Проверьте права Service Account
2. **Бот не отвечает**: Проверьте токен и добавьте бота в группу
3. **Неверный Chat ID**: Используйте отрицательное значение для групп
4. **Проблемы с датами**: Убедитесь в правильном формате (DD.MM.YYYY)
5. **Таймаут Schedule API**: Проверьте доступность внешнего API
6. **Временная зона**: Все время в CET/CEST, настройте `TIMEZONE=Europe/Berlin`

## Логи

Бот выводит подробные логи в консоль. Следите за сообщениями об ошибках для диагностики проблем.