const { config } = require('../src/config/config');
const SheetsService = require('../src/services/sheetsService');

describe('Accounts Reminder Tests', () => {
  let sheetsService;

  beforeEach(() => {
    sheetsService = new SheetsService();
  });

  describe('Configuration', () => {
    test('should have accounts column configuration', () => {
      expect(config.googleSheets.clientColumns.accounts).toBe(22);
    });

    test('should have accounts reminder minutes setting', () => {
      expect(config.monitoring.accountsReminderMinutes).toBe(30);
    });

    test('should have accounts reminder message template', () => {
      expect(typeof config.messages.accountsReminder).toBe('function');
    });
  });

  describe('Message Template', () => {
    test('should format accounts reminder message correctly', () => {
      const time = '20:30';
      const totalClients = 15;
      const message = config.messages.accountsReminder(time, totalClients);

      expect(message).toContain(`🎮 Через ${config.monitoring.accountsReminderMinutes} минут рейд/рейды`);
      expect(message).toContain('не забудьте пошарить 15 аккаунтов');
      expect(message).toContain('⏰ 20:30');
    });
  });

  describe('SheetsService', () => {
    test('should have getAccountsForUpcomingRaids method', () => {
      expect(typeof sheetsService.getAccountsForUpcomingRaids).toBe('function');
    });
  });
});
